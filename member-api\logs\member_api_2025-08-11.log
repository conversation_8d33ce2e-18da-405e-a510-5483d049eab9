2025-08-11 09:47:04 - __main__ - INFO - run_all_tests:262 - 开始执行积分相关函数测试
2025-08-11 09:47:04 - __main__ - INFO - run_all_tests:263 - 测试时间: 2025-08-11 09:47:04
2025-08-11 09:47:04 - core.database - INFO - connect:28 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-11 09:47:04 - core.database - INFO - connect:42 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-11 09:47:04 - core.database - INFO - connect:45 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-11 09:47:04 - core.database - INFO - connect:59 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-11 09:47:04 - core.database - INFO - connect:62 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-11 09:47:05 - core.database - INFO - connect:76 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-11 09:47:05 - core.database - INFO - connect:79 - 正在创建basic_info数据库连接... [企业微信数据库，用于存储企业微信相关数据]
2025-08-11 09:47:05 - core.database - INFO - connect:93 - basic_info数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/basic_info
2025-08-11 09:47:05 - core.database - INFO - connect:96 - 正在创建backend数据库连接... [企业微信和微生活对应数据库，用于存储商户映射关系]
2025-08-11 09:47:05 - core.database - INFO - connect:110 - backend数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/backend
2025-08-11 09:47:05 - core.database - INFO - connect:113 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-11 09:47:05 - core.database - INFO - connect:124 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - _test_database_connections:141 - dwoutput数据库连接测试成功: (1, 371502981, 'dwoutput')
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - _test_database_connections:148 - wedatas数据库连接测试成功: (1, 639938582, 'wedatas')
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - _test_database_connections:157 - welife_hydb数据库连接测试成功: (1,)
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - _test_database_connections:163 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - _test_database_connections:176 - basic_info数据库连接测试成功: (1, 2161636, 'basic_info')
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - _test_database_connections:183 - backend数据库连接测试成功: (1, 2161642, 'backend')
2025-08-11 09:47:05 - core.database - INFO - _test_database_connections:188 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-11 09:47:05 - __main__ - INFO - run_all_tests:268 - 数据库连接成功
2025-08-11 09:47:05 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 09:47:05 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例1 - bid=3064710828（主要测试）
2025-08-11 09:47:05 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=None
2025-08-11 09:47:05 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 3064710828, sid: None, 时间范围: 20250601-20250630
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.058秒
2025-08-11 09:47:05 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 125332
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 125332
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=None
2025-08-11 09:47:05 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 3064710828, sid: None, 时间范围: 20250601-20250630
2025-08-11 09:47:05 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 09:47:05 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:05 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.090秒
2025-08-11 09:47:05 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 1200
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 1200
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 09:47:05 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 09:47:05 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 09:47:05 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 09:47:05 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 09:47:18 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER BY a.ftime DESC) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 3064710828
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE rn = 1
            )
            SELECT
              t.credit_range,
              COUNT(DISTINCT t.uno) AS user_count_distinct,
              COUNT(*) AS record_count
            FROM (
              SELECT
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN 0
                  WHEN credit_saving BETWEEN  200 AND  399 THEN 1
                  WHEN credit_saving BETWEEN  400 AND  599 THEN 2
                  WHEN credit_saving BETWEEN  600 AND  799 THEN 3
                  WHEN credit_saving BETWEEN  800 AND  999 THEN 4
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN 5
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN 6
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN 7
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN 8
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN 9
                  ELSE 10
                END AS bucket_idx,
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN '0-199'
                  WHEN credit_saving BETWEEN  200 AND  399 THEN '200-399'
                  WHEN credit_saving BETWEEN  400 AND  599 THEN '400-599'
                  WHEN credit_saving BETWEEN  600 AND  799 THEN '600-799'
                  WHEN credit_saving BETWEEN  800 AND  999 THEN '800-999'
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN '1000-1199'
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN '1200-1399'
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN '1400-1599'
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN '1600-1799'
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN '1800-1999'
                  ELSE '>=2000'
                END AS credit_range,
                uno
              FROM picked
            ) t
            GROUP BY t.bucket_idx, t.credit_range
            ORDER BY t.bucket_idx
            
2025-08-11 09:47:18 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:18 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回10条记录，耗时12.548秒
2025-08-11 09:47:18 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 12.548秒 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 09:47:18 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回10个区间
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 10
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:133 - 积分余额分布记录:
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   1. 积分区间: 0-199, 会员人数: 33474
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   2. 积分区间: 200-399, 会员人数: 1203
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   3. 积分区间: 400-599, 会员人数: 242
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   4. 积分区间: 600-799, 会员人数: 86
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   5. 积分区间: 800-999, 会员人数: 34
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   6. 积分区间: 1000-1199, 会员人数: 23
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   7. 积分区间: 1200-1399, 会员人数: 8
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   8. 积分区间: 1400-1599, 会员人数: 8
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   9. 积分区间: 1600-1799, 会员人数: 3
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:136 -   10. 积分区间: >=2000, 会员人数: 7
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:138 - 总会员人数: 35088
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 09:47:18 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 09:47:18 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 09:47:26 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                    PARTITION BY a.uno
                    ORDER BY a.ftime DESC
                  ) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 3064710828
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE x.rn = 1
            )
            SELECT
              ccname,
              SUM(credit_saving) AS total_credit_saving,
              COUNT(*) AS user_count
            FROM picked
            GROUP BY ccname
            ORDER BY total_credit_saving DESC
            
2025-08-11 09:47:26 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:26 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回7条记录，耗时7.889秒
2025-08-11 09:47:26 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 7.889秒 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 09:47:26 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回7个等级
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 7
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:170 - 卡等级积分余额记录（前10条）:
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:172 -   1. 卡等级: 默认等级, 积分余额: 1179019, 会员数: 29778
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:172 -   2. 卡等级: 储值会员, 积分余额: 301031, 会员数: 2541
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:172 -   3. 卡等级: 普通会员, 积分余额: 266943, 会员数: 2703
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:172 -   4. 卡等级: 积分会员, 积分余额: 2840, 会员数: 33
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:172 -   5. 卡等级: 畅吃卡-来福士年, 积分余额: 673, 会员数: 2
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:172 -   6. 卡等级: 中海通专享卡, 积分余额: 0, 会员数: 30
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:172 -   7. 卡等级: 畅吃卡-安贞年卡, 积分余额: 0, 会员数: 1
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 09:47:26 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 09:47:26 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 09:47:31 - aiomysql - INFO - execute:242 - 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
                
                AND uregistered < '2025-06-30 23:59:59'
            ),
            last_ftime AS (
              SELECT uno, MAX(ftime) AS max_ftime
              FROM filtered
              GROUP BY uno
            ),
            picked AS (
              SELECT f.uno, f.ubirthday, f.credit_saving, f.ftime
              FROM filtered f
              JOIN last_ftime lf
                ON f.uno = lf.uno AND f.ftime = lf.max_ftime
            ),
            age_base AS (
              SELECT
                uno,
                credit_saving,
                CASE
                  WHEN ubirthday IS NULL THEN NULL
                  ELSE FLOOR(DATEDIFF(STR_TO_DATE(CAST(ftime AS CHAR), '%Y%m%d'), ubirthday) / 365.25)
                END AS age_years
              FROM picked
            ),
            labeled AS (
              SELECT
                CASE
                  WHEN age_years IS NULL           THEN 99
                  WHEN age_years BETWEEN  0 AND 18 THEN 0
                  WHEN age_years BETWEEN 19 AND 23 THEN 1
                  WHEN age_years BETWEEN 24 AND 30 THEN 2
                  WHEN age_years BETWEEN 31 AND 35 THEN 3
                  WHEN age_years BETWEEN 36 AND 40 THEN 4
                  WHEN age_years BETWEEN 41 AND 45 THEN 5
                  WHEN age_years BETWEEN 46 AND 50 THEN 6
                  WHEN age_years BETWEEN 51 AND 55 THEN 7
                  WHEN age_years BETWEEN 56 AND 60 THEN 8
                  WHEN age_years >= 61             THEN 9
                END AS bucket_idx,
                CASE
                  WHEN age_years IS NULL           THEN '未填写生日'
                  WHEN age_years BETWEEN  0 AND 18 THEN '0-18'
                  WHEN age_years BETWEEN 19 AND 23 THEN '19-23'
                  WHEN age_years BETWEEN 24 AND 30 THEN '24-30'
                  WHEN age_years BETWEEN 31 AND 35 THEN '31-35'
                  WHEN age_years BETWEEN 36 AND 40 THEN '36-40'
                  WHEN age_years BETWEEN 41 AND 45 THEN '41-45'
                  WHEN age_years BETWEEN 46 AND 50 THEN '46-50'
                  WHEN age_years BETWEEN 51 AND 55 THEN '51-55'
                  WHEN age_years BETWEEN 56 AND 60 THEN '56-60'
                  WHEN age_years >= 61             THEN '61及以上'
                END AS age_range,
                credit_saving
              FROM age_base
            )
            SELECT
              age_range,
              COUNT(*) AS user_count,
              SUM(credit_saving) AS total_credit_saving
            FROM labeled
            GROUP BY bucket_idx, age_range
            ORDER BY bucket_idx
            
2025-08-11 09:47:31 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:31 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回9条记录，耗时4.781秒
2025-08-11 09:47:31 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 4.781秒 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
               ...
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回9个年龄段
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 9
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:204 - 年龄段积分余额分布记录:
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   1. 年龄段: 0-18, 会员数: 7, 积分余额: 885
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   2. 年龄段: 19-23, 会员数: 4, 积分余额: 174
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   3. 年龄段: 24-30, 会员数: 22, 积分余额: 2267
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   4. 年龄段: 31-35, 会员数: 10, 积分余额: 2835
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   5. 年龄段: 36-40, 会员数: 8, 积分余额: 635
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   6. 年龄段: 41-45, 会员数: 5, 积分余额: 834
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   7. 年龄段: 46-50, 会员数: 2, 积分余额: 590
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   8. 年龄段: 51-55, 会员数: 1, 积分余额: 64
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:208 -   9. 年龄段: 未填写生日, 会员数: 35029, 积分余额: 1742222
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:211 - 总会员人数: 35088, 总积分余额: 1750506
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:245 - 测试案例1 - bid=3064710828（主要测试） - 测试结果汇总:
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 125332
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 1200
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 10
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 7
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 9
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例1 - bid=3064710828（主要测试） 全部测试通过
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例2 - 带sid过滤
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=test_sid_001
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 3064710828, sid: test_sid_001, 时间范围: 20250601-20250630
2025-08-11 09:47:31 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              AND sid = 'test_sid_001'
            
2025-08-11 09:47:31 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:31 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.046秒
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 0
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 0
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=test_sid_001
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 3064710828, sid: test_sid_001, 时间范围: 20250601-20250630
2025-08-11 09:47:31 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              AND sid = 'test_sid_001'
            
2025-08-11 09:47:31 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:31 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.039秒
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 0
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 0
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=3064710828, end_date=20250630, sid=test_sid_001
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 3064710828, sid: test_sid_001, 截止日期: 20250630
2025-08-11 09:47:31 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081109473102102700305703151530652] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 09:47:31 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081109473102102700305703151530652] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 09:47:31 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回0个区间
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 0
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=3064710828, end_date=20250630, sid=test_sid_001
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 3064710828, sid: test_sid_001, 截止日期: 20250630
2025-08-11 09:47:31 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081109473102102700706903151000855] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 09:47:31 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081109473102102700706903151000855] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 09:47:31 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回0个等级
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 0
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=3064710828, end_date=20250630, sid=test_sid_001
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 3064710828, sid: test_sid_001, 截止日期: 20250630
2025-08-11 09:47:31 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081109473102102700707003151610966] : line 0:1: Column 'sid' cannot be resolved\x00")
2025-08-11 09:47:31 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081109473102102700707003151610966] : line 0:1: Column 'sid' cannot be resolved\x00")
2025-08-11 09:47:31 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
               ...
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回0个年龄段
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 0
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:245 - 测试案例2 - 带sid过滤 - 测试结果汇总:
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 0
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 0
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 0
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 0
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 0
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例2 - 带sid过滤 全部测试通过
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例3 - 其他bid测试
2025-08-11 09:47:31 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=1113803514, start_date=20250601, end_date=20250630, sid=None
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 1113803514, sid: None, 时间范围: 20250601-20250630
2025-08-11 09:47:31 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 1113803514
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 09:47:31 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:31 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.050秒
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 395168
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 395168
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=1113803514, start_date=20250601, end_date=20250630, sid=None
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 1113803514, sid: None, 时间范围: 20250601-20250630
2025-08-11 09:47:31 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 1113803514
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 09:47:31 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:31 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.094秒
2025-08-11 09:47:31 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 42350
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 42350
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 09:47:31 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 09:47:31 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 09:47:31 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 09:47:38 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER BY a.ftime DESC) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 1113803514
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE rn = 1
            )
            SELECT
              t.credit_range,
              COUNT(DISTINCT t.uno) AS user_count_distinct,
              COUNT(*) AS record_count
            FROM (
              SELECT
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN 0
                  WHEN credit_saving BETWEEN  200 AND  399 THEN 1
                  WHEN credit_saving BETWEEN  400 AND  599 THEN 2
                  WHEN credit_saving BETWEEN  600 AND  799 THEN 3
                  WHEN credit_saving BETWEEN  800 AND  999 THEN 4
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN 5
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN 6
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN 7
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN 8
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN 9
                  ELSE 10
                END AS bucket_idx,
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN '0-199'
                  WHEN credit_saving BETWEEN  200 AND  399 THEN '200-399'
                  WHEN credit_saving BETWEEN  400 AND  599 THEN '400-599'
                  WHEN credit_saving BETWEEN  600 AND  799 THEN '600-799'
                  WHEN credit_saving BETWEEN  800 AND  999 THEN '800-999'
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN '1000-1199'
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN '1200-1399'
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN '1400-1599'
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN '1600-1799'
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN '1800-1999'
                  ELSE '>=2000'
                END AS credit_range,
                uno
              FROM picked
            ) t
            GROUP BY t.bucket_idx, t.credit_range
            ORDER BY t.bucket_idx
            
2025-08-11 09:47:38 - aiomysql - INFO - execute:243 - None
2025-08-11 09:47:38 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回11条记录，耗时7.193秒
2025-08-11 09:47:38 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 7.193秒 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 09:47:38 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回11个区间
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 11
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:133 - 积分余额分布记录:
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   1. 积分区间: 0-199, 会员人数: 1147732
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   2. 积分区间: 200-399, 会员人数: 30625
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   3. 积分区间: 400-599, 会员人数: 7138
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   4. 积分区间: 600-799, 会员人数: 2575
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   5. 积分区间: 800-999, 会员人数: 1098
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   6. 积分区间: 1000-1199, 会员人数: 673
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   7. 积分区间: 1200-1399, 会员人数: 397
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   8. 积分区间: 1400-1599, 会员人数: 226
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   9. 积分区间: 1600-1799, 会员人数: 170
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   10. 积分区间: 1800-1999, 会员人数: 134
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:136 -   11. 积分区间: >=2000, 会员人数: 527
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:138 - 总会员人数: 1191295
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 09:47:38 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 09:47:38 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 09:48:24 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                    PARTITION BY a.uno
                    ORDER BY a.ftime DESC
                  ) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 1113803514
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE x.rn = 1
            )
            SELECT
              ccname,
              SUM(credit_saving) AS total_credit_saving,
              COUNT(*) AS user_count
            FROM picked
            GROUP BY ccname
            ORDER BY total_credit_saving DESC
            
2025-08-11 09:48:24 - aiomysql - INFO - execute:243 - None
2025-08-11 09:48:24 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回8条记录，耗时45.321秒
2025-08-11 09:48:24 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 45.321秒 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 09:48:24 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回8个等级
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 8
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:170 - 卡等级积分余额记录（前10条）:
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   1. 卡等级: 默认等级, 积分余额: 15050839, 会员数: 753133
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   2. 卡等级: 普通会员, 积分余额: 4692446, 会员数: 248688
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   3. 卡等级: V1, 积分余额: 1377220, 会员数: 3083
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   4. 卡等级: 粉丝会员, 积分余额: 1339726, 会员数: 184414
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   5. 卡等级: V2, 积分余额: 721186, 会员数: 439
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   6. 卡等级: V3, 积分余额: 538388, 会员数: 93
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   7. 卡等级: 储值会员S1, 积分余额: 483106, 会员数: 1438
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:172 -   8. 卡等级: V4, 积分余额: 477809, 会员数: 7
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 09:48:24 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 09:48:24 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 09:48:29 - aiomysql - INFO - execute:242 - 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 1113803514
                
                AND uregistered < '2025-06-30 23:59:59'
            ),
            last_ftime AS (
              SELECT uno, MAX(ftime) AS max_ftime
              FROM filtered
              GROUP BY uno
            ),
            picked AS (
              SELECT f.uno, f.ubirthday, f.credit_saving, f.ftime
              FROM filtered f
              JOIN last_ftime lf
                ON f.uno = lf.uno AND f.ftime = lf.max_ftime
            ),
            age_base AS (
              SELECT
                uno,
                credit_saving,
                CASE
                  WHEN ubirthday IS NULL THEN NULL
                  ELSE FLOOR(DATEDIFF(STR_TO_DATE(CAST(ftime AS CHAR), '%Y%m%d'), ubirthday) / 365.25)
                END AS age_years
              FROM picked
            ),
            labeled AS (
              SELECT
                CASE
                  WHEN age_years IS NULL           THEN 99
                  WHEN age_years BETWEEN  0 AND 18 THEN 0
                  WHEN age_years BETWEEN 19 AND 23 THEN 1
                  WHEN age_years BETWEEN 24 AND 30 THEN 2
                  WHEN age_years BETWEEN 31 AND 35 THEN 3
                  WHEN age_years BETWEEN 36 AND 40 THEN 4
                  WHEN age_years BETWEEN 41 AND 45 THEN 5
                  WHEN age_years BETWEEN 46 AND 50 THEN 6
                  WHEN age_years BETWEEN 51 AND 55 THEN 7
                  WHEN age_years BETWEEN 56 AND 60 THEN 8
                  WHEN age_years >= 61             THEN 9
                END AS bucket_idx,
                CASE
                  WHEN age_years IS NULL           THEN '未填写生日'
                  WHEN age_years BETWEEN  0 AND 18 THEN '0-18'
                  WHEN age_years BETWEEN 19 AND 23 THEN '19-23'
                  WHEN age_years BETWEEN 24 AND 30 THEN '24-30'
                  WHEN age_years BETWEEN 31 AND 35 THEN '31-35'
                  WHEN age_years BETWEEN 36 AND 40 THEN '36-40'
                  WHEN age_years BETWEEN 41 AND 45 THEN '41-45'
                  WHEN age_years BETWEEN 46 AND 50 THEN '46-50'
                  WHEN age_years BETWEEN 51 AND 55 THEN '51-55'
                  WHEN age_years BETWEEN 56 AND 60 THEN '56-60'
                  WHEN age_years >= 61             THEN '61及以上'
                END AS age_range,
                credit_saving
              FROM age_base
            )
            SELECT
              age_range,
              COUNT(*) AS user_count,
              SUM(credit_saving) AS total_credit_saving
            FROM labeled
            GROUP BY bucket_idx, age_range
            ORDER BY bucket_idx
            
2025-08-11 09:48:29 - aiomysql - INFO - execute:243 - None
2025-08-11 09:48:29 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回11条记录，耗时5.314秒
2025-08-11 09:48:29 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.314秒 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 1113803514
               ...
2025-08-11 09:48:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回11个年龄段
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 11
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:204 - 年龄段积分余额分布记录:
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   1. 年龄段: 0-18, 会员数: 16285, 积分余额: 932243
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   2. 年龄段: 19-23, 会员数: 5550, 积分余额: 199842
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   3. 年龄段: 24-30, 会员数: 17599, 积分余额: 510146
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   4. 年龄段: 31-35, 会员数: 16118, 积分余额: 648849
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   5. 年龄段: 36-40, 会员数: 13607, 积分余额: 1417375
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   6. 年龄段: 41-45, 会员数: 7571, 积分余额: 539307
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   7. 年龄段: 46-50, 会员数: 3875, 积分余额: 345209
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   8. 年龄段: 51-55, 会员数: 1811, 积分余额: 186209
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   9. 年龄段: 56-60, 会员数: 863, 积分余额: 89016
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   10. 年龄段: 61及以上, 会员数: 645, 积分余额: 104954
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:208 -   11. 年龄段: 未填写生日, 会员数: 1107371, 积分余额: 19707570
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:211 - 总会员人数: 1191295, 总积分余额: 24680720
2025-08-11 09:48:29 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:245 - 测试案例3 - 其他bid测试 - 测试结果汇总:
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 395168
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 42350
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 11
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 8
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 11
2025-08-11 09:48:29 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例3 - 其他bid测试 全部测试通过
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:282 - 
================================================================================
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:283 - 所有测试案例执行完成 - 最终汇总
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:284 - ================================================================================
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:287 - 
测试案例1 - bid=3064710828（主要测试）:
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 125332
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 1200
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 10
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 7
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 9
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:287 - 
测试案例2 - 带sid过滤:
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 0
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 0
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 0
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 0
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 0
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:287 - 
测试案例3 - 其他bid测试:
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 395168
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 42350
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 11
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 8
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 11
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:298 - 
🎉 积分相关函数测试全部完成!
2025-08-11 09:48:29 - core.database - INFO - disconnect:205 - dwoutput数据库连接池已关闭
2025-08-11 09:48:29 - core.database - INFO - disconnect:210 - wedatas数据库连接池已关闭
2025-08-11 09:48:29 - core.database - INFO - disconnect:215 - welife_hydb数据库连接池已关闭
2025-08-11 09:48:29 - core.database - INFO - disconnect:220 - basic_info数据库连接池已关闭
2025-08-11 09:48:29 - core.database - INFO - disconnect:225 - backend数据库连接池已关闭
2025-08-11 09:48:29 - core.database - INFO - disconnect:229 - 品质收银数据库连接池已关闭
2025-08-11 09:48:29 - __main__ - INFO - run_all_tests:306 - 数据库连接已关闭
2025-08-11 10:05:19 - __main__ - INFO - run_all_tests:262 - 开始执行积分相关函数测试
2025-08-11 10:05:19 - __main__ - INFO - run_all_tests:263 - 测试时间: 2025-08-11 10:05:19
2025-08-11 10:05:19 - core.database - INFO - connect:28 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-11 10:05:19 - core.database - INFO - connect:42 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-11 10:05:19 - core.database - INFO - connect:45 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-11 10:05:20 - core.database - INFO - connect:59 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-11 10:05:20 - core.database - INFO - connect:62 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-11 10:05:20 - core.database - INFO - connect:76 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-11 10:05:20 - core.database - INFO - connect:79 - 正在创建basic_info数据库连接... [企业微信数据库，用于存储企业微信相关数据]
2025-08-11 10:05:20 - core.database - INFO - connect:93 - basic_info数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/basic_info
2025-08-11 10:05:20 - core.database - INFO - connect:96 - 正在创建backend数据库连接... [企业微信和微生活对应数据库，用于存储商户映射关系]
2025-08-11 10:05:20 - core.database - INFO - connect:110 - backend数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/backend
2025-08-11 10:05:20 - core.database - INFO - connect:113 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-11 10:05:21 - core.database - INFO - connect:124 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - _test_database_connections:141 - dwoutput数据库连接测试成功: (1, 639947297, 'dwoutput')
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - _test_database_connections:148 - wedatas数据库连接测试成功: (1, 639947298, 'wedatas')
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - _test_database_connections:157 - welife_hydb数据库连接测试成功: (1,)
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - _test_database_connections:163 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - _test_database_connections:176 - basic_info数据库连接测试成功: (1, 2181877, 'basic_info')
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - _test_database_connections:183 - backend数据库连接测试成功: (1, 2181885, 'backend')
2025-08-11 10:05:21 - core.database - INFO - _test_database_connections:188 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-11 10:05:21 - __main__ - INFO - run_all_tests:268 - 数据库连接成功
2025-08-11 10:05:21 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 10:05:21 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例1 - bid=3064710828（主要测试）
2025-08-11 10:05:21 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:05:21 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 3064710828, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.054秒
2025-08-11 10:05:21 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 125332
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 125332
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:05:21 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 3064710828, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:05:21 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:05:21 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:21 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.052秒
2025-08-11 10:05:21 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 1200
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 1200
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 10:05:21 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 10:05:21 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 10:05:21 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 10:05:21 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 10:05:24 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER BY a.ftime DESC) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 3064710828
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE rn = 1
            )
            SELECT
              t.credit_range,
              COUNT(DISTINCT t.uno) AS user_count_distinct,
              COUNT(*) AS record_count
            FROM (
              SELECT
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN 0
                  WHEN credit_saving BETWEEN  200 AND  399 THEN 1
                  WHEN credit_saving BETWEEN  400 AND  599 THEN 2
                  WHEN credit_saving BETWEEN  600 AND  799 THEN 3
                  WHEN credit_saving BETWEEN  800 AND  999 THEN 4
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN 5
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN 6
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN 7
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN 8
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN 9
                  ELSE 10
                END AS bucket_idx,
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN '0-199'
                  WHEN credit_saving BETWEEN  200 AND  399 THEN '200-399'
                  WHEN credit_saving BETWEEN  400 AND  599 THEN '400-599'
                  WHEN credit_saving BETWEEN  600 AND  799 THEN '600-799'
                  WHEN credit_saving BETWEEN  800 AND  999 THEN '800-999'
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN '1000-1199'
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN '1200-1399'
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN '1400-1599'
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN '1600-1799'
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN '1800-1999'
                  ELSE '>=2000'
                END AS credit_range,
                uno
              FROM picked
            ) t
            GROUP BY t.bucket_idx, t.credit_range
            ORDER BY t.bucket_idx
            
2025-08-11 10:05:24 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:24 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回10条记录，耗时3.640秒
2025-08-11 10:05:24 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 3.640秒 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 10:05:24 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回10个区间
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 10
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:133 - 积分余额分布记录:
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   1. 积分区间: 0-199, 会员人数: 33474
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   2. 积分区间: 200-399, 会员人数: 1203
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   3. 积分区间: 400-599, 会员人数: 242
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   4. 积分区间: 600-799, 会员人数: 86
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   5. 积分区间: 800-999, 会员人数: 34
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   6. 积分区间: 1000-1199, 会员人数: 23
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   7. 积分区间: 1200-1399, 会员人数: 8
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   8. 积分区间: 1400-1599, 会员人数: 8
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   9. 积分区间: 1600-1799, 会员人数: 3
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:136 -   10. 积分区间: >=2000, 会员人数: 7
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:138 - 总会员人数: 35088
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 10:05:24 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 10:05:24 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 10:05:30 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                    PARTITION BY a.uno
                    ORDER BY a.ftime DESC
                  ) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 3064710828
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE x.rn = 1
            )
            SELECT
              ccname,
              SUM(credit_saving) AS total_credit_saving,
              COUNT(*) AS user_count
            FROM picked
            GROUP BY ccname
            ORDER BY total_credit_saving DESC
            
2025-08-11 10:05:30 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:30 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回7条记录，耗时5.632秒
2025-08-11 10:05:30 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.632秒 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 10:05:30 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回7个等级
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 7
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:170 - 卡等级积分余额记录（前10条）:
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:172 -   1. 卡等级: 默认等级, 积分余额: 1179019, 会员数: 29778
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:172 -   2. 卡等级: 储值会员, 积分余额: 301031, 会员数: 2541
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:172 -   3. 卡等级: 普通会员, 积分余额: 266943, 会员数: 2703
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:172 -   4. 卡等级: 积分会员, 积分余额: 2840, 会员数: 33
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:172 -   5. 卡等级: 畅吃卡-来福士年, 积分余额: 673, 会员数: 2
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:172 -   6. 卡等级: 畅吃卡-安贞年卡, 积分余额: 0, 会员数: 1
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:172 -   7. 卡等级: 中海通专享卡, 积分余额: 0, 会员数: 30
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 10:05:30 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 10:05:30 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 10:05:35 - aiomysql - INFO - execute:242 - 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
                
                AND uregistered < '2025-06-30 23:59:59'
            ),
            last_ftime AS (
              SELECT uno, MAX(ftime) AS max_ftime
              FROM filtered
              GROUP BY uno
            ),
            picked AS (
              SELECT f.uno, f.ubirthday, f.credit_saving, f.ftime
              FROM filtered f
              JOIN last_ftime lf
                ON f.uno = lf.uno AND f.ftime = lf.max_ftime
            ),
            age_base AS (
              SELECT
                uno,
                credit_saving,
                CASE
                  WHEN ubirthday IS NULL THEN NULL
                  ELSE FLOOR(MONTHS_BETWEEN(TO_DATE(CAST(ftime AS STRING), 'yyyymmdd'), ubirthday) / 12)
                END AS age_years
              FROM picked
            ),
            labeled AS (
              SELECT
                CASE
                  WHEN age_years IS NULL           THEN 99
                  WHEN age_years BETWEEN  0 AND 18 THEN 0
                  WHEN age_years BETWEEN 19 AND 23 THEN 1
                  WHEN age_years BETWEEN 24 AND 30 THEN 2
                  WHEN age_years BETWEEN 31 AND 35 THEN 3
                  WHEN age_years BETWEEN 36 AND 40 THEN 4
                  WHEN age_years BETWEEN 41 AND 45 THEN 5
                  WHEN age_years BETWEEN 46 AND 50 THEN 6
                  WHEN age_years BETWEEN 51 AND 55 THEN 7
                  WHEN age_years BETWEEN 56 AND 60 THEN 8
                  WHEN age_years >= 61             THEN 9
                END AS bucket_idx,
                CASE
                  WHEN age_years IS NULL           THEN '未填写生日'
                  WHEN age_years BETWEEN  0 AND 18 THEN '0-18'
                  WHEN age_years BETWEEN 19 AND 23 THEN '19-23'
                  WHEN age_years BETWEEN 24 AND 30 THEN '24-30'
                  WHEN age_years BETWEEN 31 AND 35 THEN '31-35'
                  WHEN age_years BETWEEN 36 AND 40 THEN '36-40'
                  WHEN age_years BETWEEN 41 AND 45 THEN '41-45'
                  WHEN age_years BETWEEN 46 AND 50 THEN '46-50'
                  WHEN age_years BETWEEN 51 AND 55 THEN '51-55'
                  WHEN age_years BETWEEN 56 AND 60 THEN '56-60'
                  WHEN age_years >= 61             THEN '61及以上'
                END AS age_range,
                credit_saving
              FROM age_base
            )
            SELECT
              age_range,
              COUNT(*) AS user_count,
              SUM(credit_saving) AS total_credit_saving
            FROM labeled
            GROUP BY bucket_idx, age_range
            ORDER BY bucket_idx
            
2025-08-11 10:05:35 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:35 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回10条记录，耗时5.369秒
2025-08-11 10:05:35 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.369秒 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
               ...
2025-08-11 10:05:35 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回10个年龄段
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 10
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:204 - 年龄段积分余额分布记录:
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   1. 年龄段: , 会员数: 1, 积分余额: 0
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   2. 年龄段: 0-18, 会员数: 7, 积分余额: 931
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   3. 年龄段: 19-23, 会员数: 4, 积分余额: 243
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   4. 年龄段: 24-30, 会员数: 21, 积分余额: 2152
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   5. 年龄段: 31-35, 会员数: 11, 积分余额: 2873
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   6. 年龄段: 36-40, 会员数: 7, 积分余额: 597
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   7. 年龄段: 41-45, 会员数: 5, 积分余额: 834
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   8. 年龄段: 46-50, 会员数: 2, 积分余额: 590
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   9. 年龄段: 51-55, 会员数: 1, 积分余额: 64
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:208 -   10. 年龄段: 未填写生日, 会员数: 35029, 积分余额: 1742222
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:211 - 总会员人数: 35088, 总积分余额: 1750506
2025-08-11 10:05:35 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:245 - 测试案例1 - bid=3064710828（主要测试） - 测试结果汇总:
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 125332
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 1200
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 10
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 7
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 10
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例1 - bid=3064710828（主要测试） 全部测试通过
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例2 - 带sid过滤
2025-08-11 10:05:35 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 10:05:35 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 10:05:35 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=test_sid_001
2025-08-11 10:05:35 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 3064710828, sid: test_sid_001, 时间范围: 20250601-20250630
2025-08-11 10:05:36 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              AND sid = 'test_sid_001'
            
2025-08-11 10:05:36 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:36 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.080秒
2025-08-11 10:05:36 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 0
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 0
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=test_sid_001
2025-08-11 10:05:36 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 3064710828, sid: test_sid_001, 时间范围: 20250601-20250630
2025-08-11 10:05:36 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              AND sid = 'test_sid_001'
            
2025-08-11 10:05:36 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:36 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.142秒
2025-08-11 10:05:36 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 0
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 0
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=3064710828, end_date=20250630, sid=test_sid_001
2025-08-11 10:05:36 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 3064710828, sid: test_sid_001, 截止日期: 20250630
2025-08-11 10:05:36 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081110053602102700707203151109397] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:05:36 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081110053602102700707203151109397] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:05:36 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 10:05:36 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回0个区间
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 0
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=3064710828, end_date=20250630, sid=test_sid_001
2025-08-11 10:05:36 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 3064710828, sid: test_sid_001, 截止日期: 20250630
2025-08-11 10:05:36 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081110053602102700706403151843248] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:05:36 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081110053602102700706403151843248] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:05:36 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 10:05:36 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回0个等级
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 0
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=3064710828, end_date=20250630, sid=test_sid_001
2025-08-11 10:05:36 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 3064710828, sid: test_sid_001, 截止日期: 20250630
2025-08-11 10:05:36 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081110053602102700503103151461566] : line 0:1: Column 'sid' cannot be resolved\x00")
2025-08-11 10:05:36 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081110053602102700503103151461566] : line 0:1: Column 'sid' cannot be resolved\x00")
2025-08-11 10:05:36 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
               ...
2025-08-11 10:05:36 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回0个年龄段
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 0
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:245 - 测试案例2 - 带sid过滤 - 测试结果汇总:
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 0
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 0
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 0
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 0
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 0
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例2 - 带sid过滤 全部测试通过
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例3 - 其他bid测试
2025-08-11 10:05:36 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=1113803514, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:05:36 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 1113803514, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:05:36 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 1113803514
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:05:36 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:36 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.114秒
2025-08-11 10:05:36 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 395168
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 395168
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=1113803514, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:05:36 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 1113803514, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:05:36 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 1113803514
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:05:36 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:36 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.089秒
2025-08-11 10:05:36 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 42350
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 42350
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 10:05:36 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 10:05:36 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 10:05:36 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 10:05:42 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER BY a.ftime DESC) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 1113803514
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE rn = 1
            )
            SELECT
              t.credit_range,
              COUNT(DISTINCT t.uno) AS user_count_distinct,
              COUNT(*) AS record_count
            FROM (
              SELECT
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN 0
                  WHEN credit_saving BETWEEN  200 AND  399 THEN 1
                  WHEN credit_saving BETWEEN  400 AND  599 THEN 2
                  WHEN credit_saving BETWEEN  600 AND  799 THEN 3
                  WHEN credit_saving BETWEEN  800 AND  999 THEN 4
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN 5
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN 6
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN 7
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN 8
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN 9
                  ELSE 10
                END AS bucket_idx,
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN '0-199'
                  WHEN credit_saving BETWEEN  200 AND  399 THEN '200-399'
                  WHEN credit_saving BETWEEN  400 AND  599 THEN '400-599'
                  WHEN credit_saving BETWEEN  600 AND  799 THEN '600-799'
                  WHEN credit_saving BETWEEN  800 AND  999 THEN '800-999'
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN '1000-1199'
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN '1200-1399'
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN '1400-1599'
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN '1600-1799'
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN '1800-1999'
                  ELSE '>=2000'
                END AS credit_range,
                uno
              FROM picked
            ) t
            GROUP BY t.bucket_idx, t.credit_range
            ORDER BY t.bucket_idx
            
2025-08-11 10:05:42 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:42 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回11条记录，耗时5.926秒
2025-08-11 10:05:42 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.926秒 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 10:05:42 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回11个区间
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 11
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:133 - 积分余额分布记录:
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   1. 积分区间: 0-199, 会员人数: 1147732
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   2. 积分区间: 200-399, 会员人数: 30625
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   3. 积分区间: 400-599, 会员人数: 7138
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   4. 积分区间: 600-799, 会员人数: 2575
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   5. 积分区间: 800-999, 会员人数: 1098
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   6. 积分区间: 1000-1199, 会员人数: 673
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   7. 积分区间: 1200-1399, 会员人数: 397
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   8. 积分区间: 1400-1599, 会员人数: 226
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   9. 积分区间: 1600-1799, 会员人数: 170
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   10. 积分区间: 1800-1999, 会员人数: 134
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:136 -   11. 积分区间: >=2000, 会员人数: 527
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:138 - 总会员人数: 1191295
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 10:05:42 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 10:05:42 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 10:05:48 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                    PARTITION BY a.uno
                    ORDER BY a.ftime DESC
                  ) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 1113803514
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE x.rn = 1
            )
            SELECT
              ccname,
              SUM(credit_saving) AS total_credit_saving,
              COUNT(*) AS user_count
            FROM picked
            GROUP BY ccname
            ORDER BY total_credit_saving DESC
            
2025-08-11 10:05:48 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:48 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回8条记录，耗时5.677秒
2025-08-11 10:05:48 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.677秒 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 10:05:48 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回8个等级
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 8
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:170 - 卡等级积分余额记录（前10条）:
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   1. 卡等级: 默认等级, 积分余额: 15050839, 会员数: 753133
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   2. 卡等级: 普通会员, 积分余额: 4692446, 会员数: 248688
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   3. 卡等级: V1, 积分余额: 1377220, 会员数: 3083
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   4. 卡等级: 粉丝会员, 积分余额: 1339726, 会员数: 184414
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   5. 卡等级: V2, 积分余额: 721186, 会员数: 439
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   6. 卡等级: V3, 积分余额: 538388, 会员数: 93
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   7. 卡等级: 储值会员S1, 积分余额: 483106, 会员数: 1438
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:172 -   8. 卡等级: V4, 积分余额: 477809, 会员数: 7
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 10:05:48 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 10:05:48 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 10:05:53 - aiomysql - INFO - execute:242 - 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 1113803514
                
                AND uregistered < '2025-06-30 23:59:59'
            ),
            last_ftime AS (
              SELECT uno, MAX(ftime) AS max_ftime
              FROM filtered
              GROUP BY uno
            ),
            picked AS (
              SELECT f.uno, f.ubirthday, f.credit_saving, f.ftime
              FROM filtered f
              JOIN last_ftime lf
                ON f.uno = lf.uno AND f.ftime = lf.max_ftime
            ),
            age_base AS (
              SELECT
                uno,
                credit_saving,
                CASE
                  WHEN ubirthday IS NULL THEN NULL
                  ELSE FLOOR(MONTHS_BETWEEN(TO_DATE(CAST(ftime AS STRING), 'yyyymmdd'), ubirthday) / 12)
                END AS age_years
              FROM picked
            ),
            labeled AS (
              SELECT
                CASE
                  WHEN age_years IS NULL           THEN 99
                  WHEN age_years BETWEEN  0 AND 18 THEN 0
                  WHEN age_years BETWEEN 19 AND 23 THEN 1
                  WHEN age_years BETWEEN 24 AND 30 THEN 2
                  WHEN age_years BETWEEN 31 AND 35 THEN 3
                  WHEN age_years BETWEEN 36 AND 40 THEN 4
                  WHEN age_years BETWEEN 41 AND 45 THEN 5
                  WHEN age_years BETWEEN 46 AND 50 THEN 6
                  WHEN age_years BETWEEN 51 AND 55 THEN 7
                  WHEN age_years BETWEEN 56 AND 60 THEN 8
                  WHEN age_years >= 61             THEN 9
                END AS bucket_idx,
                CASE
                  WHEN age_years IS NULL           THEN '未填写生日'
                  WHEN age_years BETWEEN  0 AND 18 THEN '0-18'
                  WHEN age_years BETWEEN 19 AND 23 THEN '19-23'
                  WHEN age_years BETWEEN 24 AND 30 THEN '24-30'
                  WHEN age_years BETWEEN 31 AND 35 THEN '31-35'
                  WHEN age_years BETWEEN 36 AND 40 THEN '36-40'
                  WHEN age_years BETWEEN 41 AND 45 THEN '41-45'
                  WHEN age_years BETWEEN 46 AND 50 THEN '46-50'
                  WHEN age_years BETWEEN 51 AND 55 THEN '51-55'
                  WHEN age_years BETWEEN 56 AND 60 THEN '56-60'
                  WHEN age_years >= 61             THEN '61及以上'
                END AS age_range,
                credit_saving
              FROM age_base
            )
            SELECT
              age_range,
              COUNT(*) AS user_count,
              SUM(credit_saving) AS total_credit_saving
            FROM labeled
            GROUP BY bucket_idx, age_range
            ORDER BY bucket_idx
            
2025-08-11 10:05:53 - aiomysql - INFO - execute:243 - None
2025-08-11 10:05:53 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回12条记录，耗时5.682秒
2025-08-11 10:05:53 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.682秒 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 1113803514
               ...
2025-08-11 10:05:53 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回12个年龄段
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 12
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:204 - 年龄段积分余额分布记录:
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   1. 年龄段: , 会员数: 128, 积分余额: 10953
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   2. 年龄段: 0-18, 会员数: 16383, 积分余额: 941256
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   3. 年龄段: 19-23, 会员数: 6155, 积分余额: 204304
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   4. 年龄段: 24-30, 会员数: 18014, 积分余额: 526769
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   5. 年龄段: 31-35, 会员数: 16288, 积分余额: 687635
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   6. 年龄段: 36-40, 会员数: 12808, 积分余额: 1374519
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   7. 年龄段: 41-45, 会员数: 7414, 积分余额: 537468
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   8. 年龄段: 46-50, 会员数: 3613, 积分余额: 329030
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   9. 年龄段: 51-55, 会员数: 1728, 积分余额: 176358
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   10. 年龄段: 56-60, 会员数: 805, 积分余额: 87902
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   11. 年龄段: 61及以上, 会员数: 588, 积分余额: 96956
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:208 -   12. 年龄段: 未填写生日, 会员数: 1107371, 积分余额: 19707570
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:211 - 总会员人数: 1191295, 总积分余额: 24680720
2025-08-11 10:05:53 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:245 - 测试案例3 - 其他bid测试 - 测试结果汇总:
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 395168
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 42350
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 11
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 8
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 12
2025-08-11 10:05:53 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例3 - 其他bid测试 全部测试通过
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:282 - 
================================================================================
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:283 - 所有测试案例执行完成 - 最终汇总
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:284 - ================================================================================
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:287 - 
测试案例1 - bid=3064710828（主要测试）:
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 125332
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 1200
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 10
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 7
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 10
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:287 - 
测试案例2 - 带sid过滤:
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 0
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 0
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 0
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 0
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 0
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:287 - 
测试案例3 - 其他bid测试:
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 395168
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 42350
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 11
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 8
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 12
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:298 - 
🎉 积分相关函数测试全部完成!
2025-08-11 10:05:53 - core.database - INFO - disconnect:205 - dwoutput数据库连接池已关闭
2025-08-11 10:05:53 - core.database - INFO - disconnect:210 - wedatas数据库连接池已关闭
2025-08-11 10:05:53 - core.database - INFO - disconnect:215 - welife_hydb数据库连接池已关闭
2025-08-11 10:05:53 - core.database - INFO - disconnect:220 - basic_info数据库连接池已关闭
2025-08-11 10:05:53 - core.database - INFO - disconnect:225 - backend数据库连接池已关闭
2025-08-11 10:05:53 - core.database - INFO - disconnect:229 - 品质收银数据库连接池已关闭
2025-08-11 10:05:53 - __main__ - INFO - run_all_tests:306 - 数据库连接已关闭
2025-08-11 10:12:17 - __main__ - INFO - run_all_tests:262 - 开始执行积分相关函数测试
2025-08-11 10:12:17 - __main__ - INFO - run_all_tests:263 - 测试时间: 2025-08-11 10:12:17
2025-08-11 10:12:17 - core.database - INFO - connect:28 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-11 10:12:18 - core.database - INFO - connect:42 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-11 10:12:18 - core.database - INFO - connect:45 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-11 10:12:18 - core.database - INFO - connect:59 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-11 10:12:18 - core.database - INFO - connect:62 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-11 10:12:18 - core.database - INFO - connect:76 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-11 10:12:18 - core.database - INFO - connect:79 - 正在创建basic_info数据库连接... [企业微信数据库，用于存储企业微信相关数据]
2025-08-11 10:12:18 - core.database - INFO - connect:93 - basic_info数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/basic_info
2025-08-11 10:12:18 - core.database - INFO - connect:96 - 正在创建backend数据库连接... [企业微信和微生活对应数据库，用于存储商户映射关系]
2025-08-11 10:12:18 - core.database - INFO - connect:110 - backend数据库连接池创建成功: rm-2zety0946b3t6t764ko.mysql.rds.aliyuncs.com:3306/backend
2025-08-11 10:12:18 - core.database - INFO - connect:113 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-11 10:12:19 - core.database - INFO - connect:124 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - _test_database_connections:141 - dwoutput数据库连接测试成功: (1, 639949889, 'dwoutput')
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - _test_database_connections:148 - wedatas数据库连接测试成功: (1, 371514275, 'wedatas')
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - _test_database_connections:157 - welife_hydb数据库连接测试成功: (1,)
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - _test_database_connections:163 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - _test_database_connections:176 - basic_info数据库连接测试成功: (1, 2189423, 'basic_info')
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - _test_database_connections:183 - backend数据库连接测试成功: (1, 2189434, 'backend')
2025-08-11 10:12:19 - core.database - INFO - _test_database_connections:188 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-11 10:12:19 - __main__ - INFO - run_all_tests:268 - 数据库连接成功
2025-08-11 10:12:19 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 10:12:19 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例1 - bid=3064710828（主要测试）
2025-08-11 10:12:19 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:12:19 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 3064710828, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.039秒
2025-08-11 10:12:19 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 125332
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 125332
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:12:19 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 3064710828, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:12:19 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:12:19 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:19 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.040秒
2025-08-11 10:12:19 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 1200
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 1200
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 10:12:19 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 10:12:19 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 10:12:19 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 10:12:19 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 10:12:22 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER BY a.ftime DESC) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 3064710828
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE rn = 1
            )
            SELECT
              t.credit_range,
              COUNT(DISTINCT t.uno) AS user_count_distinct,
              COUNT(*) AS record_count
            FROM (
              SELECT
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN 0
                  WHEN credit_saving BETWEEN  200 AND  399 THEN 1
                  WHEN credit_saving BETWEEN  400 AND  599 THEN 2
                  WHEN credit_saving BETWEEN  600 AND  799 THEN 3
                  WHEN credit_saving BETWEEN  800 AND  999 THEN 4
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN 5
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN 6
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN 7
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN 8
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN 9
                  ELSE 10
                END AS bucket_idx,
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN '0-199'
                  WHEN credit_saving BETWEEN  200 AND  399 THEN '200-399'
                  WHEN credit_saving BETWEEN  400 AND  599 THEN '400-599'
                  WHEN credit_saving BETWEEN  600 AND  799 THEN '600-799'
                  WHEN credit_saving BETWEEN  800 AND  999 THEN '800-999'
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN '1000-1199'
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN '1200-1399'
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN '1400-1599'
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN '1600-1799'
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN '1800-1999'
                  ELSE '>=2000'
                END AS credit_range,
                uno
              FROM picked
            ) t
            GROUP BY t.bucket_idx, t.credit_range
            ORDER BY t.bucket_idx
            
2025-08-11 10:12:22 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:22 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回10条记录，耗时3.174秒
2025-08-11 10:12:22 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 3.174秒 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 10:12:22 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回10个区间
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 10
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:133 - 积分余额分布记录:
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   1. 积分区间: 0-199, 会员人数: 33474
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   2. 积分区间: 200-399, 会员人数: 1203
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   3. 积分区间: 400-599, 会员人数: 242
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   4. 积分区间: 600-799, 会员人数: 86
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   5. 积分区间: 800-999, 会员人数: 34
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   6. 积分区间: 1000-1199, 会员人数: 23
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   7. 积分区间: 1200-1399, 会员人数: 8
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   8. 积分区间: 1400-1599, 会员人数: 8
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   9. 积分区间: 1600-1799, 会员人数: 3
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:136 -   10. 积分区间: >=2000, 会员人数: 7
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:138 - 总会员人数: 35088
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 10:12:22 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 10:12:22 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 10:12:26 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                    PARTITION BY a.uno
                    ORDER BY a.ftime DESC
                  ) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 3064710828
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE x.rn = 1
            )
            SELECT
              ccname,
              SUM(credit_saving) AS total_credit_saving,
              COUNT(*) AS user_count
            FROM picked
            GROUP BY ccname
            ORDER BY total_credit_saving DESC
            
2025-08-11 10:12:26 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:26 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回7条记录，耗时3.530秒
2025-08-11 10:12:26 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 3.530秒 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 10:12:26 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回7个等级
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 7
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:170 - 卡等级积分余额记录（前10条）:
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:172 -   1. 卡等级: 默认等级, 积分余额: 1179019, 会员数: 29778
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:172 -   2. 卡等级: 储值会员, 积分余额: 301031, 会员数: 2541
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:172 -   3. 卡等级: 普通会员, 积分余额: 266943, 会员数: 2703
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:172 -   4. 卡等级: 积分会员, 积分余额: 2840, 会员数: 33
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:172 -   5. 卡等级: 畅吃卡-来福士年, 积分余额: 673, 会员数: 2
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:172 -   6. 卡等级: 畅吃卡-安贞年卡, 积分余额: 0, 会员数: 1
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:172 -   7. 卡等级: 中海通专享卡, 积分余额: 0, 会员数: 30
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 10:12:26 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=3064710828, end_date=20250630, sid=None
2025-08-11 10:12:26 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 3064710828, sid: None, 截止日期: 20250630
2025-08-11 10:12:29 - aiomysql - INFO - execute:242 - 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
                
                AND uregistered < '2025-06-30 23:59:59'
            ),
            last_ftime AS (
              SELECT uno, MAX(ftime) AS max_ftime
              FROM filtered
              GROUP BY uno
            ),
            picked AS (
              SELECT f.uno, f.ubirthday, f.credit_saving, f.ftime
              FROM filtered f
              JOIN last_ftime lf
                ON f.uno = lf.uno AND f.ftime = lf.max_ftime
            ),
            age_base AS (
              SELECT
                uno,
                credit_saving,
                CASE
                  WHEN ubirthday IS NULL THEN NULL
                  ELSE FLOOR(MONTHS_BETWEEN(TO_DATE(CAST(ftime AS STRING), 'yyyymmdd'), ubirthday) / 12)
                END AS age_years
              FROM picked
            ),
            labeled AS (
              SELECT
                CASE
                  WHEN age_years IS NULL           THEN 99
                  WHEN age_years BETWEEN  0 AND 18 THEN 0
                  WHEN age_years BETWEEN 19 AND 23 THEN 1
                  WHEN age_years BETWEEN 24 AND 30 THEN 2
                  WHEN age_years BETWEEN 31 AND 35 THEN 3
                  WHEN age_years BETWEEN 36 AND 40 THEN 4
                  WHEN age_years BETWEEN 41 AND 45 THEN 5
                  WHEN age_years BETWEEN 46 AND 50 THEN 6
                  WHEN age_years BETWEEN 51 AND 55 THEN 7
                  WHEN age_years BETWEEN 56 AND 60 THEN 8
                  WHEN age_years >= 61             THEN 9
                END AS bucket_idx,
                CASE
                  WHEN age_years IS NULL           THEN '未填写生日'
                  WHEN age_years BETWEEN  0 AND 18 THEN '0-18'
                  WHEN age_years BETWEEN 19 AND 23 THEN '19-23'
                  WHEN age_years BETWEEN 24 AND 30 THEN '24-30'
                  WHEN age_years BETWEEN 31 AND 35 THEN '31-35'
                  WHEN age_years BETWEEN 36 AND 40 THEN '36-40'
                  WHEN age_years BETWEEN 41 AND 45 THEN '41-45'
                  WHEN age_years BETWEEN 46 AND 50 THEN '46-50'
                  WHEN age_years BETWEEN 51 AND 55 THEN '51-55'
                  WHEN age_years BETWEEN 56 AND 60 THEN '56-60'
                  WHEN age_years >= 61             THEN '61及以上'
                END AS age_range,
                credit_saving
              FROM age_base
            )
            SELECT
              age_range,
              COUNT(*) AS user_count,
              SUM(credit_saving) AS total_credit_saving
            FROM labeled
            GROUP BY bucket_idx, age_range
            ORDER BY bucket_idx
            
2025-08-11 10:12:29 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:29 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回10条记录，耗时3.227秒
2025-08-11 10:12:29 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 3.227秒 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
               ...
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回10个年龄段
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 10
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:204 - 年龄段积分余额分布记录:
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   1. 年龄段: , 会员数: 1, 积分余额: 0
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   2. 年龄段: 0-18, 会员数: 7, 积分余额: 931
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   3. 年龄段: 19-23, 会员数: 4, 积分余额: 243
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   4. 年龄段: 24-30, 会员数: 21, 积分余额: 2152
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   5. 年龄段: 31-35, 会员数: 11, 积分余额: 2873
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   6. 年龄段: 36-40, 会员数: 7, 积分余额: 597
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   7. 年龄段: 41-45, 会员数: 5, 积分余额: 834
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   8. 年龄段: 46-50, 会员数: 2, 积分余额: 590
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   9. 年龄段: 51-55, 会员数: 1, 积分余额: 64
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:208 -   10. 年龄段: 未填写生日, 会员数: 35029, 积分余额: 1742222
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:211 - 总会员人数: 35088, 总积分余额: 1750506
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:245 - 测试案例1 - bid=3064710828（主要测试） - 测试结果汇总:
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 125332
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 1200
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 10
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 7
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 10
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例1 - bid=3064710828（主要测试） 全部测试通过
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例2 - 带sid过滤
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=4173789868
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 3064710828, sid: 4173789868, 时间范围: 20250601-20250630
2025-08-11 10:12:29 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              AND sid = '4173789868'
            
2025-08-11 10:12:29 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:29 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.053秒
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 55131
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 55131
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=3064710828, start_date=20250601, end_date=20250630, sid=4173789868
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 3064710828, sid: 4173789868, 时间范围: 20250601-20250630
2025-08-11 10:12:29 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 3064710828
              AND ftime BETWEEN 20250601 AND 20250630
              AND sid = '4173789868'
            
2025-08-11 10:12:29 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:29 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.048秒
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 0
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 0
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=3064710828, end_date=20250630, sid=4173789868
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 3064710828, sid: 4173789868, 截止日期: 20250630
2025-08-11 10:12:29 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081110122902102700707103151789449] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:12:29 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081110122902102700707103151789449] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:12:29 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回0个区间
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 0
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=3064710828, end_date=20250630, sid=4173789868
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 3064710828, sid: 4173789868, 截止日期: 20250630
2025-08-11 10:12:29 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081110122902102700706403151870688] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:12:29 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081110122902102700706403151870688] : Column 'a.sid' cannot be resolved\x00")
2025-08-11 10:12:29 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回0个等级
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 0
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=3064710828, end_date=20250630, sid=4173789868
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 3064710828, sid: 4173789868, 截止日期: 20250630
2025-08-11 10:12:29 - core.database - ERROR - get_welife_hydb_connection:267 - welife_hydb数据库操作异常: (1815, "[20038, 2025081110122902102700502003151021285] : line 0:1: Column 'sid' cannot be resolved\x00")
2025-08-11 10:12:29 - core.database - ERROR - execute_welife_hydb_query:417 - welife_hydb数据库查询失败: (1815, "[20038, 2025081110122902102700502003151021285] : line 0:1: Column 'sid' cannot be resolved\x00")
2025-08-11 10:12:29 - core.database - ERROR - execute_welife_hydb_query:418 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 3064710828
               ...
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回0个年龄段
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 0
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:245 - 测试案例2 - 带sid过滤 - 测试结果汇总:
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 55131
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 0
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 0
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 0
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 0
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例2 - 带sid过滤 全部测试通过
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:222 - 
============================================================
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:223 - 开始执行: 测试案例3 - 其他bid测试
2025-08-11 10:12:29 - __main__ - INFO - run_single_test_case:224 - ============================================================
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:70 - === 测试赠送积分统计 ===
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:71 - 参数: bid=1113803514, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:38 - 执行赠送积分统计查询 - bid: 1113803514, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:12:29 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 1113803514
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:12:29 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:29 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.051秒
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_reward:46 - 赠送积分统计查询完成，结果: 395168
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:75 - 赠送积分统计结果: 395168
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:76 - 结果类型: <class 'int'>
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_reward:82 - ✅ 赠送积分统计测试通过
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:92 - === 测试使用积分统计 ===
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:93 - 参数: bid=1113803514, start_date=20250601, end_date=20250630, sid=None
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:81 - 执行使用积分统计查询 - bid: 1113803514, sid: None, 时间范围: 20250601-20250630
2025-08-11 10:12:29 - aiomysql - INFO - execute:242 - 
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = 1113803514
              AND ftime BETWEEN 20250601 AND 20250630
              
            
2025-08-11 10:12:29 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:29 - core.database - INFO - execute_welife_hydb_one:433 - welife_hydb单条查询完成，结果: 有数据，耗时0.042秒
2025-08-11 10:12:29 - api.query.Credit.CreditCountSql - INFO - get_total_credit_consume:89 - 使用积分统计查询完成，结果: 42350
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:97 - 使用积分统计结果: 42350
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:98 - 结果类型: <class 'int'>
2025-08-11 10:12:29 - __main__ - INFO - test_total_credit_consume:104 - ✅ 使用积分统计测试通过
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_distribution:114 - === 测试积分余额分布 ===
2025-08-11 10:12:29 - __main__ - INFO - test_credit_balance_distribution:115 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 10:12:29 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:88 - 执行积分余额分布查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 10:12:34 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER BY a.ftime DESC) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 1113803514
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE rn = 1
            )
            SELECT
              t.credit_range,
              COUNT(DISTINCT t.uno) AS user_count_distinct,
              COUNT(*) AS record_count
            FROM (
              SELECT
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN 0
                  WHEN credit_saving BETWEEN  200 AND  399 THEN 1
                  WHEN credit_saving BETWEEN  400 AND  599 THEN 2
                  WHEN credit_saving BETWEEN  600 AND  799 THEN 3
                  WHEN credit_saving BETWEEN  800 AND  999 THEN 4
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN 5
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN 6
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN 7
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN 8
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN 9
                  ELSE 10
                END AS bucket_idx,
                CASE
                  WHEN credit_saving BETWEEN    0 AND  199 THEN '0-199'
                  WHEN credit_saving BETWEEN  200 AND  399 THEN '200-399'
                  WHEN credit_saving BETWEEN  400 AND  599 THEN '400-599'
                  WHEN credit_saving BETWEEN  600 AND  799 THEN '600-799'
                  WHEN credit_saving BETWEEN  800 AND  999 THEN '800-999'
                  WHEN credit_saving BETWEEN 1000 AND 1199 THEN '1000-1199'
                  WHEN credit_saving BETWEEN 1200 AND 1399 THEN '1200-1399'
                  WHEN credit_saving BETWEEN 1400 AND 1599 THEN '1400-1599'
                  WHEN credit_saving BETWEEN 1600 AND 1799 THEN '1600-1799'
                  WHEN credit_saving BETWEEN 1800 AND 1999 THEN '1800-1999'
                  ELSE '>=2000'
                END AS credit_range,
                uno
              FROM picked
            ) t
            GROUP BY t.bucket_idx, t.credit_range
            ORDER BY t.bucket_idx
            
2025-08-11 10:12:34 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:34 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回11条记录，耗时4.479秒
2025-08-11 10:12:34 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 4.479秒 - SQL: 
            WITH picked AS (
              SELECT uno, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (PARTITION BY a.uno ORDER B...
2025-08-11 10:12:34 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_distribution:102 - 积分余额分布查询完成，返回11个区间
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:119 - 积分余额分布结果数量: 11
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:120 - 结果类型: <class 'list'>
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:133 - 积分余额分布记录:
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   1. 积分区间: 0-199, 会员人数: 1147732
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   2. 积分区间: 200-399, 会员人数: 30625
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   3. 积分区间: 400-599, 会员人数: 7138
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   4. 积分区间: 600-799, 会员人数: 2575
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   5. 积分区间: 800-999, 会员人数: 1098
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   6. 积分区间: 1000-1199, 会员人数: 673
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   7. 积分区间: 1200-1399, 会员人数: 397
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   8. 积分区间: 1400-1599, 会员人数: 226
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   9. 积分区间: 1600-1799, 会员人数: 170
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   10. 积分区间: 1800-1999, 会员人数: 134
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:136 -   11. 积分区间: >=2000, 会员人数: 527
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:138 - 总会员人数: 1191295
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_distribution:140 - ✅ 积分余额分布测试通过
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_by_level:150 - === 测试卡等级积分余额 ===
2025-08-11 10:12:34 - __main__ - INFO - test_credit_balance_by_level:151 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 10:12:34 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:160 - 执行卡等级积分余额查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 10:12:39 - aiomysql - INFO - execute:242 - 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                    PARTITION BY a.uno
                    ORDER BY a.ftime DESC
                  ) AS rn
                FROM dws_user_info_charges_credit_summary_d a
                WHERE a.bid = 1113803514
                  
                  AND a.uregistered < '2025-06-30 23:59:59'
              ) x
              WHERE x.rn = 1
            )
            SELECT
              ccname,
              SUM(credit_saving) AS total_credit_saving,
              COUNT(*) AS user_count
            FROM picked
            GROUP BY ccname
            ORDER BY total_credit_saving DESC
            
2025-08-11 10:12:39 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:39 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回8条记录，耗时5.720秒
2025-08-11 10:12:39 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.720秒 - SQL: 
            WITH picked AS (
              SELECT uno, ccname, credit_saving
              FROM (
                SELECT
                  a.*,
                  ROW_NUMBER() OVER (
                 ...
2025-08-11 10:12:39 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_level:174 - 卡等级积分余额查询完成，返回8个等级
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:155 - 卡等级积分余额结果数量: 8
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:156 - 结果类型: <class 'list'>
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:170 - 卡等级积分余额记录（前10条）:
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   1. 卡等级: 默认等级, 积分余额: 15050839, 会员数: 753133
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   2. 卡等级: 普通会员, 积分余额: 4692446, 会员数: 248688
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   3. 卡等级: V1, 积分余额: 1377220, 会员数: 3083
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   4. 卡等级: 粉丝会员, 积分余额: 1339726, 会员数: 184414
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   5. 卡等级: V2, 积分余额: 721186, 会员数: 439
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   6. 卡等级: V3, 积分余额: 538388, 会员数: 93
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   7. 卡等级: 储值会员S1, 积分余额: 483106, 会员数: 1438
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:172 -   8. 卡等级: V4, 积分余额: 477809, 会员数: 7
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_level:174 - ✅ 卡等级积分余额测试通过
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_age:184 - === 测试年龄段积分余额分布 ===
2025-08-11 10:12:39 - __main__ - INFO - test_credit_balance_by_age:185 - 参数: bid=1113803514, end_date=20250630, sid=None
2025-08-11 10:12:39 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:275 - 执行年龄段积分余额分布查询 - bid: 1113803514, sid: None, 截止日期: 20250630
2025-08-11 10:12:45 - aiomysql - INFO - execute:242 - 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 1113803514
                
                AND uregistered < '2025-06-30 23:59:59'
            ),
            last_ftime AS (
              SELECT uno, MAX(ftime) AS max_ftime
              FROM filtered
              GROUP BY uno
            ),
            picked AS (
              SELECT f.uno, f.ubirthday, f.credit_saving, f.ftime
              FROM filtered f
              JOIN last_ftime lf
                ON f.uno = lf.uno AND f.ftime = lf.max_ftime
            ),
            age_base AS (
              SELECT
                uno,
                credit_saving,
                CASE
                  WHEN ubirthday IS NULL THEN NULL
                  ELSE FLOOR(MONTHS_BETWEEN(TO_DATE(CAST(ftime AS STRING), 'yyyymmdd'), ubirthday) / 12)
                END AS age_years
              FROM picked
            ),
            labeled AS (
              SELECT
                CASE
                  WHEN age_years IS NULL           THEN 99
                  WHEN age_years BETWEEN  0 AND 18 THEN 0
                  WHEN age_years BETWEEN 19 AND 23 THEN 1
                  WHEN age_years BETWEEN 24 AND 30 THEN 2
                  WHEN age_years BETWEEN 31 AND 35 THEN 3
                  WHEN age_years BETWEEN 36 AND 40 THEN 4
                  WHEN age_years BETWEEN 41 AND 45 THEN 5
                  WHEN age_years BETWEEN 46 AND 50 THEN 6
                  WHEN age_years BETWEEN 51 AND 55 THEN 7
                  WHEN age_years BETWEEN 56 AND 60 THEN 8
                  WHEN age_years >= 61             THEN 9
                END AS bucket_idx,
                CASE
                  WHEN age_years IS NULL           THEN '未填写生日'
                  WHEN age_years BETWEEN  0 AND 18 THEN '0-18'
                  WHEN age_years BETWEEN 19 AND 23 THEN '19-23'
                  WHEN age_years BETWEEN 24 AND 30 THEN '24-30'
                  WHEN age_years BETWEEN 31 AND 35 THEN '31-35'
                  WHEN age_years BETWEEN 36 AND 40 THEN '36-40'
                  WHEN age_years BETWEEN 41 AND 45 THEN '41-45'
                  WHEN age_years BETWEEN 46 AND 50 THEN '46-50'
                  WHEN age_years BETWEEN 51 AND 55 THEN '51-55'
                  WHEN age_years BETWEEN 56 AND 60 THEN '56-60'
                  WHEN age_years >= 61             THEN '61及以上'
                END AS age_range,
                credit_saving
              FROM age_base
            )
            SELECT
              age_range,
              COUNT(*) AS user_count,
              SUM(credit_saving) AS total_credit_saving
            FROM labeled
            GROUP BY bucket_idx, age_range
            ORDER BY bucket_idx
            
2025-08-11 10:12:45 - aiomysql - INFO - execute:243 - None
2025-08-11 10:12:45 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回12条记录，耗时5.785秒
2025-08-11 10:12:45 - core.database - WARNING - execute_welife_hydb_query:412 - welife_hydb慢查询检测: 5.785秒 - SQL: 
            WITH filtered AS (
              SELECT uno, ubirthday, credit_saving, ftime
              FROM dws_user_info_charges_credit_summary_d
              WHERE bid = 1113803514
               ...
2025-08-11 10:12:45 - api.query.Credit.CreditStatisticsSql - INFO - get_credit_balance_by_age:289 - 年龄段积分余额分布查询完成，返回12个年龄段
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:189 - 年龄段积分余额分布结果数量: 12
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:190 - 结果类型: <class 'list'>
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:204 - 年龄段积分余额分布记录:
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   1. 年龄段: , 会员数: 128, 积分余额: 10953
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   2. 年龄段: 0-18, 会员数: 16383, 积分余额: 941256
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   3. 年龄段: 19-23, 会员数: 6155, 积分余额: 204304
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   4. 年龄段: 24-30, 会员数: 18014, 积分余额: 526769
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   5. 年龄段: 31-35, 会员数: 16288, 积分余额: 687635
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   6. 年龄段: 36-40, 会员数: 12808, 积分余额: 1374519
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   7. 年龄段: 41-45, 会员数: 7414, 积分余额: 537468
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   8. 年龄段: 46-50, 会员数: 3613, 积分余额: 329030
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   9. 年龄段: 51-55, 会员数: 1728, 积分余额: 176358
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   10. 年龄段: 56-60, 会员数: 805, 积分余额: 87902
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   11. 年龄段: 61及以上, 会员数: 588, 积分余额: 96956
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:208 -   12. 年龄段: 未填写生日, 会员数: 1107371, 积分余额: 19707570
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:211 - 总会员人数: 1191295, 总积分余额: 24680720
2025-08-11 10:12:45 - __main__ - INFO - test_credit_balance_by_age:213 - ✅ 年龄段积分余额分布测试通过
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:244 - 
========================================
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:245 - 测试案例3 - 其他bid测试 - 测试结果汇总:
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:246 - ========================================
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:247 - 赠送积分总数: 395168
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:248 - 使用积分总数: 42350
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:249 - 积分余额分布区间数: 11
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:250 - 卡等级数量: 8
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:251 - 年龄段数量: 12
2025-08-11 10:12:45 - __main__ - INFO - run_single_test_case:253 - ✅ 测试案例3 - 其他bid测试 全部测试通过
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:282 - 
================================================================================
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:283 - 所有测试案例执行完成 - 最终汇总
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:284 - ================================================================================
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:287 - 
测试案例1 - bid=3064710828（主要测试）:
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 125332
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 1200
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 10
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 7
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 10
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:287 - 
测试案例2 - 带sid过滤:
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 55131
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 0
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 0
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 0
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 0
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:287 - 
测试案例3 - 其他bid测试:
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:291 -   ✅ 执行成功
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:292 -   赠送积分总数: 395168
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:293 -   使用积分总数: 42350
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:294 -   积分余额分布区间数: 11
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:295 -   卡等级数量: 8
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:296 -   年龄段数量: 12
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:298 - 
🎉 积分相关函数测试全部完成!
2025-08-11 10:12:45 - core.database - INFO - disconnect:205 - dwoutput数据库连接池已关闭
2025-08-11 10:12:45 - core.database - INFO - disconnect:210 - wedatas数据库连接池已关闭
2025-08-11 10:12:45 - core.database - INFO - disconnect:215 - welife_hydb数据库连接池已关闭
2025-08-11 10:12:45 - core.database - INFO - disconnect:220 - basic_info数据库连接池已关闭
2025-08-11 10:12:45 - core.database - INFO - disconnect:225 - backend数据库连接池已关闭
2025-08-11 10:12:45 - core.database - INFO - disconnect:229 - 品质收银数据库连接池已关闭
2025-08-11 10:12:45 - __main__ - INFO - run_all_tests:306 - 数据库连接已关闭
