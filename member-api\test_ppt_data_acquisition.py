#!/usr/bin/env python3
"""
PPT数据获取测试脚本
测试首次消费和再次消费数据是否正确获取
"""

import asyncio
import logging
from core.models import QueryParams
from api.PPTreport.DataAcquisition import data_acquisition_service

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def test_ppt_data_acquisition():
    """测试PPT数据获取"""
    
    # 创建测试查询参数
    query_params = QueryParams(
        query_type="custom",
        bid="3064710828",
        sid=None,
        start_date="2025-06-01",
        end_date="2025-06-30",
        cashier_system="welife",
        merchant_id=None
    )
    
    logger.info("🛡️ 开始PPT数据获取测试")
    logger.info(f"📋 测试条件: bid={query_params.bid}, 时间范围={query_params.start_date}-{query_params.end_date}")
    
    try:
        # 仅获取会员消费数据进行测试
        logger.info("开始获取会员消费数据...")
        member_consume_data = await data_acquisition_service.get_member_consume_data_only(query_params)
        
        # 检查首次消费和再次消费数据
        first_consume_amount = member_consume_data.get('first_consume_amount', 'N/A')
        repeat_consume_amount = member_consume_data.get('repeat_consume_amount', 'N/A')
        total_actual_amount = member_consume_data.get('total_real_income', 'N/A')
        
        # 输出结果
        logger.info("="*60)
        logger.info("📊 会员消费数据测试结果")
        logger.info("="*60)
        logger.info(f"💰 首次消费金额: {first_consume_amount}")
        logger.info(f"💰 再次消费金额: {repeat_consume_amount}")
        logger.info(f"💰 会员总实收金额: {total_actual_amount}")
        logger.info(f"📊 数据字段总数: {len(member_consume_data)}")
        logger.info("="*60)
        
        # 验证结果
        if first_consume_amount != 'N/A' and first_consume_amount is not None:
            logger.info("✅ 首次消费金额数据获取成功")
        else:
            logger.warning("⚠️  首次消费金额数据缺失")
            
        if repeat_consume_amount != 'N/A' and repeat_consume_amount is not None:
            logger.info("✅ 再次消费金额数据获取成功")
        else:
            logger.warning("⚠️  再次消费金额数据缺失")
            
        return member_consume_data
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return None
        
    finally:
        logger.info("🏁 PPT数据获取测试完成")

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(test_ppt_data_acquisition())
    
    if result is not None:
        print(f"\n🎯 测试完成，获取到 {len(result)} 个数据字段")
        
        # 显示关键字段
        key_fields = ['first_consume_amount', 'repeat_consume_amount', 'total_real_income']
        for field in key_fields:
            value = result.get(field, 'N/A')
            print(f"  {field}: {value}")
    else:
        print("\n❌ 测试失败，请查看日志了解详情")
