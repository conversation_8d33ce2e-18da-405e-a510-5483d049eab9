#!/usr/bin/env python3
"""
首次消费金额测试脚本 - 专门测试 bid=3064710828
测试时间范围：20250601-20250630

直接调用 MemberConsumeSqlAdd 类中的优化版本函数
函数名保持为 get_dwoutput_first_consume_amount_sql
"""

import asyncio
import logging

# 导入会员消费查询类
from api.query.MemberConsumeSqlAdd import MemberConsumeSqlAddQueries

# 配置日志 - 记录实际发生时间
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)



async def test_first_consume_calculation():
    """测试首次消费金额计算"""
    
    # 指定的测试参数
    bid = "3064710828"
    start_date = "20250601"
    end_date = "20250630"
    
    logger.info("🛡️ 开始首次消费金额测试")
    logger.info(f"📋 测试条件: bid={bid}, 时间范围={start_date}-{end_date}")
    logger.info("⏰ 开始记录实际执行时间")
    
    try:
        # 执行首次消费金额计算 - 调用类中的优化版本函数
        first_consume_amount = await MemberConsumeSqlAddQueries.get_dwoutput_first_consume_amount_sql(
            start_date=start_date,
            end_date=end_date,
            bid=bid
        )
        
        # 输出最终结果
        logger.info("="*60)
        logger.info("📊 测试结果汇总")
        logger.info("="*60)
        logger.info(f"📊 品牌ID: {bid}")
        logger.info(f"📊 时间范围: {start_date} - {end_date}")
        logger.info(f"💰 首次消费金额: {first_consume_amount:.2f}")
        logger.info("="*60)
        
        # 验证结果
        if first_consume_amount > 0:
            logger.info("✅ 测试成功：首次消费金额计算完成，结果大于0")
        elif first_consume_amount == 0:
            logger.info("⚠️  测试完成：首次消费金额为0，可能该时间范围内没有首次消费记录")
        else:
            logger.warning("⚠️  测试完成：首次消费金额为负数，可能存在较多取消消费记录")
            
        return first_consume_amount
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return None
        
    finally:
        logger.info("🏁 首次消费金额测试完成")

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(test_first_consume_calculation())
    
    if result is not None:
        print(f"\n🎯 最终结果: 首次消费金额 = {result:.2f}")
    else:
        print("\n❌ 测试失败，请查看日志了解详情")
