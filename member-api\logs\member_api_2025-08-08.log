2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240131 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240229 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240201 AND 20240229 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年2月 数据: 充值实收7400.00, 消耗储值5103.00, 留存率31.04%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年3月 充值消费数据: 2024-03-01 到 2024-03-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240331
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20240301 AND 20240331
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.023秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240229 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240331 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240301 AND 20240331 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年3月 数据: 充值实收14500.00, 消耗储值11259.28, 留存率22.35%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年4月 充值消费数据: 2024-04-01 到 2024-04-30
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240430
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20240401 AND 20240430
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240331 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240430 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240401 AND 20240430 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年4月 数据: 充值实收59648.00, 消耗储值40100.59, 留存率32.77%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年5月 充值消费数据: 2024-05-01 到 2024-05-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240531
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20240501 AND 20240531
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240430 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240531 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240501 AND 20240531 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年5月 数据: 充值实收71400.00, 消耗储值52804.14, 留存率26.04%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年6月 充值消费数据: 2024-06-01 到 2024-06-30
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240630
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20240601 AND 20240630
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240531 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240601 AND 20240630 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年6月 数据: 充值实收70600.00, 消耗储值58921.62, 留存率16.54%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年7月 充值消费数据: 2024-07-01 到 2024-07-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240731
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20240701 AND 20240731
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240731 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240731 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.021秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年7月 数据: 充值实收81900.00, 消耗储值68953.68, 留存率15.81%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年8月 充值消费数据: 2024-08-01 到 2024-08-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240831
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20240801 AND 20240831
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240731 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240831 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240801 AND 20240831 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年8月 数据: 充值实收65500.00, 消耗储值60987.39, 留存率6.89%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年9月 充值消费数据: 2024-09-01 到 2024-09-30
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240930
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20240901 AND 20240930
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240831 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240930 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240901 AND 20240930 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年9月 数据: 充值实收62600.00, 消耗储值55398.13, 留存率11.50%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年10月 充值消费数据: 2024-10-01 到 2024-10-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20241031
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20241001 AND 20241031
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240930 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20241031 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20241001 AND 20241031 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年10月 数据: 充值实收52500.00, 消耗储值47711.31, 留存率9.12%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年11月 充值消费数据: 2024-11-01 到 2024-11-30
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20241130
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20241101 AND 20241130
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20241031 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20241130 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20241101 AND 20241130 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年11月 数据: 充值实收58500.00, 消耗储值54806.87, 留存率6.31%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2024年12月 充值消费数据: 2024-12-01 到 2024-12-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20241231
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20241201 AND 20241231
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.023秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20241130 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20241231 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20241201 AND 20241231 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.025秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2024年12月 数据: 充值实收47200.00, 消耗储值44500.79, 留存率5.72%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:369 - 充值消费数据获取完成，共 12 条记录
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年1月 充值消费数据: 2025-01-01 到 2025-01-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250131
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250101 AND 20250131
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20241231 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250131 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250101 AND 20250131 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年1月 数据: 充值实收50400.00, 消耗储值44212.82, 留存率12.28%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年2月 充值消费数据: 2025-02-01 到 2025-02-28
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250228
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250201 AND 20250228
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250131 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250228 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250201 AND 20250228 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.020秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年2月 数据: 充值实收57400.00, 消耗储值46720.85, 留存率18.60%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年3月 充值消费数据: 2025-03-01 到 2025-03-31
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250331
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250301 AND 20250331
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.006秒
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250228 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250331 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250301 AND 20250331 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年3月 数据: 充值实收65600.00, 消耗储值58132.66, 留存率11.38%
2025-08-08 10:41:29 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年4月 充值消费数据: 2025-04-01 到 2025-04-30
2025-08-08 10:41:29 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250430
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250401 AND 20250430
          AND bid = **********
          
        
2025-08-08 10:41:29 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:29 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.029秒
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250331 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250430 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250401 AND 20250430 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.020秒
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年4月 数据: 充值实收71100.00, 消耗储值57548.53, 留存率19.06%
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年5月 充值消费数据: 2025-05-01 到 2025-05-31
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250531
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250501 AND 20250531
          AND bid = **********
          
        
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250430 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250531 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250501 AND 20250531 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年5月 数据: 充值实收49600.00, 消耗储值46729.10, 留存率5.79%
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年6月 充值消费数据: 2025-06-01 到 2025-06-30
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250630
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250601 AND 20250630
          AND bid = **********
          
        
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250531 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250601 AND 20250630 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年6月 数据: 充值实收50500.00, 消耗储值45267.09, 留存率10.36%
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年7月 充值消费数据: 2025-07-01 到 2025-07-31
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250731
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250701 AND 20250731
          AND bid = **********
          
        
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250731 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250731 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年7月 数据: 充值实收57300.00, 消耗储值49579.49, 留存率13.47%
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:316 - 获取 2025年8月 充值消费数据: 2025-08-01 到 2025-08-07
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
        SELECT
          
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        ,
          
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        ,
          
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        ,
          
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        ,
          
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250807
         AND bid = **********
         ) AS total_charge_amount_unused
        
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN 20250801 AND 20250807
          AND bid = **********
          
        
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-08 10:41:30 - aiomysql - INFO - execute:242 - 
                    SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250731 AND bid = ********** )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250807 AND bid = ********** )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250801 AND 20250807 AND bid = ********** )
        ) AS total_consume_prepay_used
        
                    
2025-08-08 10:41:30 - aiomysql - INFO - execute:243 - None
2025-08-08 10:41:30 - core.database - INFO - execute_dwoutput_one:347 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:350 - 2025年8月 数据: 充值实收11900.00, 消耗储值10719.97, 留存率9.92%
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _fetch_monthly_data:369 - 充值消费数据获取完成，共 8 条记录
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - generate_member_charge_charts:106 - 数据获取结果 - 去年: 12条, 今年: 8条
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - generate_member_charge_charts:124 - 最终数据 - 去年: 12条, 今年: 8条
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _add_data_table:562 - 横向数据表格创建完成，包含 12 个月的数据
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:470 - 准备保存图片到: uploads\temp\**********_20250808\member_charge_last_year.png
2025-08-08 10:41:30 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:475 - 保存目录: uploads\temp\**********_20250808, 存在: True
2025-08-08 10:41:31 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:481 - matplotlib保存完成
2025-08-08 10:41:31 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:486 - 图表生成成功: uploads\temp\**********_20250808\member_charge_last_year.png, 文件大小: 343442 字节
2025-08-08 10:41:31 - api.PPTreport.picture.MemberChargePic - INFO - _add_data_table:562 - 横向数据表格创建完成，包含 8 个月的数据
2025-08-08 10:41:31 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:470 - 准备保存图片到: uploads\temp\**********_20250808\member_charge_this_year.png
2025-08-08 10:41:31 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:475 - 保存目录: uploads\temp\**********_20250808, 存在: True
2025-08-08 10:41:32 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:481 - matplotlib保存完成
2025-08-08 10:41:32 - api.PPTreport.picture.MemberChargePic - INFO - _generate_chart:486 - 图表生成成功: uploads\temp\**********_20250808\member_charge_this_year.png, 文件大小: 312811 字节
2025-08-08 10:41:32 - services.llm_service - INFO - __init__:78 - LLMService初始化完成，使用模型: qwen-max
2025-08-08 10:41:32 - api.PPTreport.picture.PictureAi - INFO - __init__:28 - 图片AI分析器初始化完成
2025-08-08 10:41:32 - api.PPTreport.picture.MemberChargePic - INFO - generate_member_charge_charts:150 - 开始生成会员充值消费AI分析...
2025-08-08 10:41:32 - api.PPTreport.picture.MemberChargePic - INFO - generate_member_charge_charts:155 - 数据完整，生成完整AI分析
2025-08-08 10:41:32 - api.PPTreport.picture.MemberChargeAi - INFO - generate_all_member_charge_analysis:169 - 开始生成会员充值消费AI分析...
2025-08-08 10:41:32 - services.llm_service - INFO - call:229 - 调用LLM，模型: qwen-max, enable_search=False
2025-08-08 10:41:32 - services.llm_service - INFO - call:230 - 消息: [{"role": "user", "content": "\n作为专业的数据分析师，请基于以下会员充值消费数据进行深入分析：\n存储留存率 = (期间充值实收总金额-期间消耗储值实收总金额)/期间充值实收总金额\n数据概况：\n数据期间：12个月\n充值实收：总计601048.00万元，月均50087.33万元\n储值消耗：总计507573.50万元，消耗率84.45%\n充值赠送：总计0.00...
2025-08-08 10:42:15 - services.llm_service - INFO - call:247 - LLM响应: ChatCompletion(id='chatcmpl-f53a306f-27f0-9f7a-9333-cd688a74374f', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='1. 2024年7月充值金额达到峰值81900.00万元远超其他月份，建议详细分析该月营销活动与用户互动策略，并在接下来的几个月如2024年1月至3月期间复用这些成功经验以提升整体充值水平\n2. 储值消耗率高达84.45%，显示用户对储值服务的高度认可，但为了进一步提高用户粘性，建议从2024年第二季度开始推出更多个性化或限时优惠储值套餐吸引更多消费\n3. 当前无任何充值赠送活动导致赠送比例为0.00%，这可能限制了部分用户的充值积极性，提议自2024年4月起实施小额随机红包返还机制，比如每满1000元随机返现10-50元，以此激励更多小额及中等额度的充值行为\n4. 2024年4月留存率达到最高点32.77%，而到了同年12月则降至最低5.72%，表明年内存在显著波动，应深入研究4月份采取的具体措施（例如是否有特别促销、客户服务改进等），并考虑在2024年下半年特别是10至12月间复制这些有效做法来稳定甚至提升留存率', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754620935, model='qwen-max', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=277, prompt_tokens=1045, total_tokens=1322, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=0)))
2025-08-08 10:42:15 - api.PPTreport.picture.MemberChargeAi - INFO - analyze_member_charge_last_year_data:69 - 去年会员充值消费数据AI分析完成
2025-08-08 10:42:15 - services.llm_service - INFO - call:229 - 调用LLM，模型: qwen-max, enable_search=False
2025-08-08 10:42:15 - services.llm_service - INFO - call:230 - 消息: [{"role": "user", "content": "\n作为专业的数据分析师，请基于以下会员充值消费数据进行深入的对比分析，主要关注近期的变化给出改进建议：\n存储留存率 = (期间充值实收总金额-期间消耗储值实收总金额)/期间充值实收总金额\n今年数据概况：\n数据期间：8个月\n充值实收：总计413800.00万元，月均51725.00万元\n储值消耗：总计358910.51万元，消耗...
2025-08-08 10:42:35 - services.llm_service - INFO - call:247 - LLM响应: ChatCompletion(id='chatcmpl-8c7d3f2d-8d81-9a10-a821-9feb0eabd789', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='1. 2025年充值规模同比增加8.82%，但增长放缓，建议引入灵活多变的充值套餐和促销活动以刺激消费\n2. 2025年的储值消耗率比去年同期提高6.49个百分点至86.74%，表明用户活跃度提升，建议进一步丰富储值应用场景，比如增加会员专享服务或商品\n3. 连续两年充值赠送均为零，考虑到赠送策略对吸引新用户及激励老用户的有效性，建议启动基于消费行为分析的小额随机红包或积分奖励计划\n4. 与去年相比，2025年的平均储值留存率下降了9.37个百分点，特别是从2024年4月的32.77%降至2025年4月的19.06%，反映出客户忠诚度可能有所下滑，应加强个性化营销和售后服务，比如提供专属客服支持、建立VIP俱乐部等措施来增强用户粘性', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754620956, model='qwen-max', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=211, prompt_tokens=1341, total_tokens=1552, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=0)))
2025-08-08 10:42:35 - api.PPTreport.picture.MemberChargeAi - INFO - analyze_member_charge_this_year_data:140 - 今年会员充值消费数据AI分析完成
2025-08-08 10:42:35 - api.PPTreport.picture.MemberChargeAi - INFO - generate_all_member_charge_analysis:188 - 会员充值消费AI分析生成完成
2025-08-08 10:42:35 - api.PPTreport.picture.MemberChargePic - INFO - generate_member_charge_charts:182 - 会员充值消费AI分析生成完成
2025-08-08 10:42:35 - api.PPTreport.picture.MemberChargePic - INFO - generate_member_charge_charts:190 - 会员充值消费图表生成完成，共生成 4 个结果
2025-08-08 10:42:35 - api.PPTreport.PicAcquisition - INFO - _generate_member_charge_pictures:234 - 会员充值消费图片生成完成: ['member_charge_last_year', 'member_charge_this_year', 'member_charge_last_year_analysis_report', 'member_charge_this_year_analysis_report']
2025-08-08 10:42:35 - api.PPTreport.picture.LevelConsumptionPic - INFO - generate_level_consumption_chart:68 - 开始生成会员等级消费分析图表 - bid: **********
2025-08-08 10:42:35 - api.PPTreport.picture.LevelConsumptionPic - INFO - generate_level_consumption_chart:69 - 查询参数类型: <class 'core.models.QueryParams'>
2025-08-08 10:42:35 - api.PPTreport.picture.LevelConsumptionPic - INFO - generate_level_consumption_chart:70 - 图片管理器会话目录: uploads\temp\**********_20250808
2025-08-08 10:42:35 - api.PPTreport.picture.LevelConsumptionPic - INFO - generate_level_consumption_chart:78 - 查询时间范围: 2025-01-01 到 2025-06-30
2025-08-08 10:42:35 - api.PPTreport.picture.LevelConsumptionPic - INFO - generate_level_consumption_chart:79 - 品牌ID: **********, 门店ID: None
2025-08-08 10:42:35 - api.PPTreport.picture.LevelConsumptionPic - INFO - _fetch_level_consumption_data:181 - 开始获取会员等级消费数据: 2025-01-01 到 2025-06-30
2025-08-08 10:42:35 - aiomysql - INFO - execute:242 - 
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = **********
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                
                AND a.tccreated BETWEEN '2025-01-01 00:00:00' AND '2025-06-30 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(CASE WHEN num > 0 THEN 1 END) AS cnum,                            -- 消费会员数
                SUM(tcTotalFee) AS tcTotalFee                                        -- 消费金额
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(ROUND(a.tcTotalFee / a.cnum, 2), 0) AS perCapitaConsumption       -- 人均消费额
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        ORDER BY a.ccids
        
2025-08-08 10:42:35 - aiomysql - INFO - execute:243 - None
2025-08-08 10:42:35 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.301秒
2025-08-08 10:42:35 - api.PPTreport.picture.LevelConsumptionPic - INFO - _fetch_level_consumption_data:186 - 人均消费额数据获取完成，共 6 条记录
2025-08-08 10:42:36 - aiomysql - INFO - execute:242 - 
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = **********
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                
                AND a.tccreated BETWEEN '2025-01-01 00:00:00' AND '2025-06-30 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(num) AS ccount,                                                  -- 消费订单数
                SUM(tcTotalFee) AS tcTotalFee                                        -- 消费金额
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(ROUND(a.tcTotalFee / a.ccount, 2), 0) AS customerUnitPrice        -- 客单价
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        ORDER BY a.ccids
        
2025-08-08 10:42:36 - aiomysql - INFO - execute:243 - None
2025-08-08 10:42:36 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.200秒
2025-08-08 10:42:36 - api.PPTreport.picture.LevelConsumptionPic - INFO - _fetch_level_consumption_data:191 - 客单价数据获取完成，共 6 条记录
2025-08-08 10:42:36 - aiomysql - INFO - execute:242 - 
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = **********
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                
                AND a.tccreated BETWEEN '2025-01-01 00:00:00' AND '2025-06-30 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(CASE WHEN num > 0 THEN 1 END) AS cnum,                            -- 消费会员数
                SUM(num) AS ccount                                                   -- 消费订单数
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(ROUND(a.ccount / a.cnum, 2), 0) AS avgConsumFrequency             -- 平均消费频次
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        ORDER BY a.ccids
        
2025-08-08 10:42:36 - aiomysql - INFO - execute:243 - None
2025-08-08 10:42:36 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.176秒
2025-08-08 10:42:36 - api.PPTreport.picture.LevelConsumptionPic - INFO - _fetch_level_consumption_data:196 - 平均消费频次数据获取完成，共 6 条记录
2025-08-08 10:42:36 - api.PPTreport.picture.LevelConsumptionPic - INFO - _merge_level_data:244 - 数据合并完成，包含等级: ['中海通专享卡', '储值会员', '普通会员', '畅吃卡-安贞年卡', '畅吃卡-来福士年', '积分会员']
2025-08-08 10:42:36 - api.PPTreport.picture.LevelConsumptionPic - INFO - _fetch_level_consumption_data:201 - 会员等级消费数据合并完成，共 6 个等级
2025-08-08 10:42:36 - api.PPTreport.picture.LevelConsumptionPic - INFO - _generate_chart:274 - 准备生成图表，包含 6 个会员等级
2025-08-08 10:42:36 - api.PPTreport.picture.LevelConsumptionPic - INFO - _add_data_table:415 - 横向数据表格创建完成，包含 6 个会员等级的数据
2025-08-08 10:42:37 - api.PPTreport.picture.LevelConsumptionPic - INFO - _generate_chart:347 - 会员等级消费分析图表生成完成: uploads\temp\**********_20250808\level_consumption.png
2025-08-08 10:42:37 - services.llm_service - INFO - __init__:78 - LLMService初始化完成，使用模型: qwen-max
2025-08-08 10:42:37 - api.PPTreport.picture.PictureAi - INFO - __init__:28 - 图片AI分析器初始化完成
2025-08-08 10:42:37 - api.PPTreport.picture.PictureAi - INFO - generate_level_consumption_analysis:631 - 开始生成会员等级消费AI分析...
2025-08-08 10:42:37 - services.llm_service - INFO - call:229 - 调用LLM，模型: qwen-max, enable_search=False
2025-08-08 10:42:37 - services.llm_service - INFO - call:230 - 消息: [{"role": "user", "content": "\n作为专业的会员运营数据分析师，请基于以下会员等级消费数据进行深入分析：\n\n分析时间范围：2025-01-01 至 2025-06-30\n\n数据概况：\n数据时间范围：2025-01-01 至 2025-06-30\n等级数量：6个会员等级\n等级名称：中海通专享卡, 储值会员, 普通会员, 畅吃卡-安贞年卡, 畅吃卡-来福士年...
2025-08-08 10:42:53 - services.llm_service - INFO - call:247 - LLM响应: ChatCompletion(id='chatcmpl-a592d22d-369b-9f14-b945-2ebf29b76200', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='在2025-01-01至2025-06-30期间，储值会员以人均消费额178.87元领先，表明其作为高价值群体的重要性。普通会员与之相比存在显著差距，人均消费仅80.97元，揭示了会员体系中潜在的价值分层机会。从整体看，等级间的人均消费额、客单价及消费频次差异明显，这不仅验证了当前会员分层的有效性，也提示了优化空间。建议针对储值会员推出个性化增值服务或专属活动，增强其品牌归属感；同时，对于低活跃度的普通会员，可以通过设置积分奖励机制或阶段性促销活动激发其消费潜力。此外，应定期回顾各等级会员的行为模式，适时调整权益配置，确保持续吸引并保留高质量用户。', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754620973, model='qwen-max', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=182, prompt_tokens=709, total_tokens=891, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=0)))
2025-08-08 10:42:53 - api.PPTreport.picture.LevelConsumptionAi - INFO - analyze_level_consumption_data:90 - 会员等级消费数据AI分析完成，分析长度: 277字
2025-08-08 10:42:53 - api.PPTreport.picture.PictureAi - INFO - generate_level_consumption_analysis:639 - 会员等级消费AI分析生成完成
2025-08-08 10:42:53 - api.PPTreport.picture.LevelConsumptionPic - INFO - generate_level_consumption_chart:116 - 会员等级消费AI分析生成完成
2025-08-08 10:42:53 - api.PPTreport.picture.LevelConsumptionPic - INFO - generate_level_consumption_chart:121 - 会员等级消费分析图表生成完成，共生成 2 个结果
2025-08-08 10:42:53 - api.PPTreport.PicAcquisition - INFO - _generate_level_consumption_pictures:262 - 会员等级消费分析图片生成完成: ['level_consumption', 'level_consumption_analysis_report']
2025-08-08 10:42:53 - api.PPTreport.picture.LevelOrderPic - INFO - generate_level_order_chart:68 - 开始生成会员等级订单分析图表 - bid: **********
2025-08-08 10:42:53 - api.PPTreport.picture.LevelOrderPic - INFO - generate_level_order_chart:69 - 查询参数类型: <class 'core.models.QueryParams'>
2025-08-08 10:42:53 - api.PPTreport.picture.LevelOrderPic - INFO - generate_level_order_chart:70 - 图片管理器会话目录: uploads\temp\**********_20250808
2025-08-08 10:42:53 - api.PPTreport.picture.LevelOrderPic - INFO - generate_level_order_chart:78 - 查询时间范围: 2025-01-01 到 2025-06-30
2025-08-08 10:42:53 - api.PPTreport.picture.LevelOrderPic - INFO - generate_level_order_chart:79 - 品牌ID: **********, 门店ID: None
2025-08-08 10:42:53 - api.PPTreport.picture.LevelOrderPic - INFO - _fetch_level_order_data:181 - 开始获取会员等级订单数据: 2025-01-01 到 2025-06-30
2025-08-08 10:42:53 - aiomysql - INFO - execute:242 - 
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = **********
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                
                AND a.tccreated BETWEEN '2025-01-01 00:00:00' AND '2025-06-30 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(tcTotalFee) AS tcTotalFee                                        -- 消费金额
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(CONCAT(CAST((a.tcTotalFee / SUM(a.tcTotalFee) OVER()) * 100 AS DECIMAL(10, 2)), '%'), '0.00%') AS orderMoneyRatio  -- 消费金额占比
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        ORDER BY a.ccids
        
2025-08-08 10:42:53 - aiomysql - INFO - execute:243 - None
2025-08-08 10:42:53 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.286秒
2025-08-08 10:42:53 - api.PPTreport.picture.LevelOrderPic - INFO - _fetch_level_order_data:186 - 消费金额占比数据获取完成，共 6 条记录
2025-08-08 10:42:54 - aiomysql - INFO - execute:242 - 
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = **********
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                
                AND a.tccreated BETWEEN '2025-01-01 00:00:00' AND '2025-06-30 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(num) AS ccount                                                   -- 消费订单数
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(CONCAT(CAST((a.ccount / SUM(a.ccount) OVER()) * 100 AS DECIMAL(10, 2)), '%'), '0.00%') AS orderNumRatio            -- 消费订单占比
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        ORDER BY a.ccids
        
2025-08-08 10:42:54 - aiomysql - INFO - execute:243 - None
2025-08-08 10:42:54 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.278秒
2025-08-08 10:42:54 - api.PPTreport.picture.LevelOrderPic - INFO - _fetch_level_order_data:191 - 消费订单占比数据获取完成，共 6 条记录
2025-08-08 10:42:54 - aiomysql - INFO - execute:242 - 
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = **********
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                
                AND a.tccreated BETWEEN '2025-01-01 00:00:00' AND '2025-06-30 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                COUNT(DISTINCT CASE WHEN num > 1 THEN uid END) AS ccnum,              -- 复购会员数
                SUM(CASE WHEN num > 0 THEN 1 END) AS cnum                            -- 消费会员数
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(CONCAT(CAST((a.ccnum / a.cnum) * 100 AS DECIMAL(10, 2)), '%'), '0.00%') AS repurchaseRate                           -- 复购率
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        ORDER BY a.ccids
        
2025-08-08 10:42:54 - aiomysql - INFO - execute:243 - None
2025-08-08 10:42:54 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.233秒
2025-08-08 10:42:54 - api.PPTreport.picture.LevelOrderPic - INFO - _fetch_level_order_data:196 - 复购率数据获取完成，共 6 条记录
2025-08-08 10:42:54 - api.PPTreport.picture.LevelOrderPic - INFO - _merge_level_order_data:264 - 数据合并完成，包含等级: ['中海通专享卡', '储值会员', '普通会员', '畅吃卡-安贞年卡', '畅吃卡-来福士年', '积分会员']
2025-08-08 10:42:54 - api.PPTreport.picture.LevelOrderPic - INFO - _fetch_level_order_data:201 - 会员等级订单数据合并完成，共 6 个等级
2025-08-08 10:42:54 - api.PPTreport.picture.LevelOrderPic - INFO - _generate_chart:294 - 准备生成图表，包含 6 个会员等级
2025-08-08 10:42:54 - api.PPTreport.picture.LevelOrderPic - INFO - _add_data_table:435 - 横向数据表格创建完成，包含 6 个会员等级的数据
2025-08-08 10:42:55 - api.PPTreport.picture.LevelOrderPic - INFO - _generate_chart:367 - 会员等级订单分析图表生成完成: uploads\temp\**********_20250808\level_order.png
2025-08-08 10:42:55 - services.llm_service - INFO - __init__:78 - LLMService初始化完成，使用模型: qwen-max
2025-08-08 10:42:55 - api.PPTreport.picture.PictureAi - INFO - __init__:28 - 图片AI分析器初始化完成
2025-08-08 10:42:55 - api.PPTreport.picture.PictureAi - INFO - generate_level_order_analysis:675 - 开始生成会员等级订单AI分析...
2025-08-08 10:42:55 - services.llm_service - INFO - call:229 - 调用LLM，模型: qwen-max, enable_search=False
2025-08-08 10:42:55 - services.llm_service - INFO - call:230 - 消息: [{"role": "user", "content": "\n作为专业的会员运营数据分析师，请基于以下会员等级订单数据进行深入分析：\n\n分析时间范围：2025-01-01 至 2025-06-30\n\n数据概况：\n数据时间范围：2025-01-01 至 2025-06-30\n等级数量：6个会员等级\n等级名称：中海通专享卡, 储值会员, 普通会员, 畅吃卡-安贞年卡, 畅吃卡-来福士年...
2025-08-08 10:43:12 - services.llm_service - INFO - call:247 - LLM响应: ChatCompletion(id='chatcmpl-b6edb881-cfb7-9543-a9e5-ff412a653c3e', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='在2025年上半年，普通会员展现出显著的消费能力和订单贡献，分别占总消费金额和订单量的63.73%与65.60%，但其复购率仅为22.89%，远低于其他等级，表明虽然吸引了大量一次性消费者，但忠诚度提升空间大。相比之下，畅吃卡-安贞年卡尽管消费金额占比仅1.66%，订单占比也较低为1.90%，却拥有最高的复购率66.43%，显示出极高的用户粘性。储值会员表现均衡，消费与订单贡献均超过三分之一，且复购率达到56.80%，是维持业绩稳定的关键力量。针对上述情况，建议通过优化普通会员体验增加复购激励措施，同时加大畅吃卡-安贞年卡推广力度，吸引更多长期价值客户；对于储值会员，则应保持现有优势，并探索更多增值服务以进一步提高其活跃度。', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754620992, model='qwen-max', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=204, prompt_tokens=562, total_tokens=766, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=0)))
2025-08-08 10:43:12 - api.PPTreport.picture.LevelOrderAi - INFO - analyze_level_order_data:83 - 会员等级订单数据AI分析完成，分析长度: 316字
2025-08-08 10:43:12 - api.PPTreport.picture.PictureAi - INFO - generate_level_order_analysis:683 - 会员等级订单AI分析生成完成
2025-08-08 10:43:12 - api.PPTreport.picture.LevelOrderPic - INFO - generate_level_order_chart:116 - 会员等级订单AI分析生成完成
2025-08-08 10:43:12 - api.PPTreport.picture.LevelOrderPic - INFO - generate_level_order_chart:121 - 会员等级订单分析图表生成完成，共生成 2 个结果
2025-08-08 10:43:12 - api.PPTreport.PicAcquisition - INFO - _generate_level_order_pictures:290 - 会员等级订单分析图片生成完成: ['level_order', 'level_order_analysis_report']
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:68 - 开始生成会员等级人数分析图表 - bid: **********
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:69 - 查询参数类型: <class 'core.models.QueryParams'>
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:70 - 图片管理器会话目录: uploads\temp\**********_20250808
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:78 - 查询时间范围: 2025-01-01 到 2025-06-30
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:79 - 品牌ID: **********, 门店ID: None
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:90 - 历史会员数查询日期: 2024-12-31
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:91 - 期末会员数查询日期: 2025-06-30
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - _fetch_level_number_data:193 - 开始获取会员等级人数数据: 历史日期 2024-12-31, 期末日期 2025-06-30
2025-08-08 10:43:12 - aiomysql - INFO - execute:242 - 
        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            COUNT(DISTINCT a.uid) AS member_count
        FROM welifehy_welife_users a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccid = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        WHERE a.bid = **********
          AND a.uRegistered <= '2024-12-31 23:59:59'
          
        GROUP BY ccName
        ORDER BY member_count DESC
        
2025-08-08 10:43:12 - aiomysql - INFO - execute:243 - None
2025-08-08 10:43:12 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.221秒
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - _fetch_level_number_data:198 - 历史会员数数据获取完成，共 6 条记录
2025-08-08 10:43:12 - aiomysql - INFO - execute:242 - 
        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            COUNT(DISTINCT a.uid) AS member_count
        FROM welifehy_welife_users a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccid = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = **********
        WHERE a.bid = **********
          AND a.uRegistered <= '2025-06-30 23:59:59'
          
        GROUP BY ccName
        ORDER BY member_count DESC
        
2025-08-08 10:43:12 - aiomysql - INFO - execute:243 - None
2025-08-08 10:43:12 - core.database - INFO - execute_welife_hydb_query:408 - welife_hydb查询完成，返回6条记录，耗时0.194秒
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - _fetch_level_number_data:203 - 期末会员数数据获取完成，共 6 条记录
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - _merge_level_number_data:247 - 数据合并完成，包含等级: ['中海通专享卡', '储值会员', '普通会员', '畅吃卡-安贞年卡', '畅吃卡-来福士年', '积分会员']
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - _fetch_level_number_data:208 - 会员等级人数数据合并完成，共 6 个等级
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - _generate_chart:276 - 准备生成图表，包含 6 个会员等级
2025-08-08 10:43:12 - api.PPTreport.picture.LevelNumberPic - INFO - _add_data_table:392 - 横向数据表格创建完成，包含 6 个会员等级的数据
2025-08-08 10:43:13 - api.PPTreport.picture.LevelNumberPic - INFO - _generate_chart:326 - 会员等级人数分析图表生成完成: uploads\temp\**********_20250808\level_number.png
2025-08-08 10:43:13 - services.llm_service - INFO - __init__:78 - LLMService初始化完成，使用模型: qwen-max
2025-08-08 10:43:13 - api.PPTreport.picture.PictureAi - INFO - __init__:28 - 图片AI分析器初始化完成
2025-08-08 10:43:13 - api.PPTreport.picture.PictureAi - INFO - generate_level_number_analysis:719 - 开始生成会员等级人数AI分析...
2025-08-08 10:43:13 - services.llm_service - INFO - call:229 - 调用LLM，模型: qwen-max, enable_search=False
2025-08-08 10:43:13 - services.llm_service - INFO - call:230 - 消息: [{"role": "user", "content": "\n作为专业的会员运营数据分析师，请基于以下会员等级人数对比数据进行深入分析：\n\n对比分析时间：历史基准日期 2024-12-31 与期末统计日期 2025-06-30\n\n数据概况：\n对比时间：2024-12-31 至 2025-06-30\n等级数量：6个会员等级\n等级名称：中海通专享卡, 储值会员, 普通会员, 畅吃卡-安...
2025-08-08 10:43:32 - services.llm_service - INFO - call:247 - LLM响应: ChatCompletion(id='chatcmpl-5720d086-3928-9241-a775-19d1231a3aba', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='从2024年12月31日至2025年6月30日，会员总数由25929人增长至35088人，增幅达35.32%，其中普通会员增加8357人成为最大增长点，表明基础用户群体的显著扩张。储值会员和积分会员分别以45.1%和50.0%的增长率紧随其后，显示出较高忠诚度用户的积极反馈。然而，中海通专享卡及两种畅吃卡会员数量保持不变，暗示这些高端或特定需求服务可能面临市场饱和或吸引力不足的问题。建议针对未见增长的等级进行服务内容优化或市场调研，探索潜在客户需求；同时，利用数据分析进一步挖掘普通会员向更高级别转化的可能性，比如通过个性化推荐、定制化优惠等方式促进升级，从而实现会员结构的整体优化与价值提升。', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754621012, model='qwen-max', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=194, prompt_tokens=519, total_tokens=713, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=0)))
2025-08-08 10:43:32 - api.PPTreport.picture.LevelNumberAi - INFO - analyze_level_number_data:84 - 会员等级人数数据AI分析完成，分析长度: 299字
2025-08-08 10:43:32 - api.PPTreport.picture.PictureAi - INFO - generate_level_number_analysis:727 - 会员等级人数AI分析生成完成
2025-08-08 10:43:32 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:128 - 会员等级人数AI分析生成完成
2025-08-08 10:43:32 - api.PPTreport.picture.LevelNumberPic - INFO - generate_level_number_chart:133 - 会员等级人数分析图表生成完成，共生成 2 个结果
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - _generate_level_number_pictures:318 - 会员等级人数分析图片生成完成: ['level_number', 'level_number_analysis_report']
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - generate_all_pictures:94 - PPT图片生成完成 - 共生成 26 张图片
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:350 - 更新图片路径: image_new_member_add_last_year -> uploads\temp\**********_20250808\new_member_add_last_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:350 - 更新图片路径: image_new_member_add_this_year -> uploads\temp\**********_20250808\new_member_add_this_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:357 - 更新图片路径: image_member_consumption_last_year -> uploads\temp\**********_20250808\member_consumption_last_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:357 - 更新图片路径: image_member_consumption_this_year -> uploads\temp\**********_20250808\member_consumption_this_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:364 - 更新图片路径: image_avg_consumption_last_year -> uploads\temp\**********_20250808\avg_consumption_last_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:364 - 更新图片路径: image_avg_consumption_this_year -> uploads\temp\**********_20250808\avg_consumption_this_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:371 - 更新图片路径: image_consumption_num_last_year -> uploads\temp\**********_20250808\consumption_num_last_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:371 - 更新图片路径: image_consumption_num_this_year -> uploads\temp\**********_20250808\consumption_num_this_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:378 - 更新图片路径: image_member_charge_last_year -> uploads\temp\**********_20250808\member_charge_last_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:378 - 更新图片路径: image_member_charge_this_year -> uploads\temp\**********_20250808\member_charge_this_year.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:385 - 更新图片路径: image_level_consumption -> uploads\temp\**********_20250808\level_consumption.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:392 - 更新图片路径: image_level_order -> uploads\temp\**********_20250808\level_order.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:399 - 更新图片路径: image_level_number -> uploads\temp\**********_20250808\level_number.png
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: new_member_add_last_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: new_member_add_this_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: member_consumption_last_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: member_consumption_this_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: avg_consumption_last_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: avg_consumption_this_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: consumption_num_last_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: consumption_num_this_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: member_charge_last_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: member_charge_this_year_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: level_consumption_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: level_order_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:421 - 添加AI分析参数: level_number_analysis_report
2025-08-08 10:43:32 - api.PPTreport.PicAcquisition - INFO - get_picture_paths:423 - 图片和AI分析生成完成，共返回 32 个参数
2025-08-08 10:43:32 - api.PPTreport.PPTReport - INFO - _convert_to_ppt_params:141 - 图片生成完成，共 32 张图片
2025-08-08 10:43:32 - api.PPTreport.constants - INFO - get_coupon_analysis_params:327 - 生成优惠券参数完成 - 实际优惠券数量: 10, 生成参数数量: 50
2025-08-08 10:43:32 - api.PPTreport.constants - INFO - get_coupon_analysis_params:328 - 汇总统计 - 总发券量: 16630张, 总使用量: 1854张, 平均使用率: 11.1%
2025-08-08 10:43:32 - api.PPTreport.constants - INFO - get_pinzhi_cashier_params:225 - 品智收银参数生成完成: {'total_actual_revenue': '0.00', 'total_expected_revenue': '0.00', 'dine_in_actual_revenue': '0.00', 'takeout_actual_revenue': '0.00', 'non_member_total_actual_amount': '0.00', 'discount_rate': '0.00%', 'member_dine_in_ratio': '0.00%'}
2025-08-08 10:43:32 - api.PPTreport.constants - INFO - build_complete_ppt_data:405 - 品智收银数据已集成到PPT参数中
2025-08-08 10:43:32 - api.PPTreport.constants - INFO - build_complete_ppt_data:409 - 开始处理行业分析数据，数据量: 24
2025-08-08 10:43:32 - api.PPTreport.constants - INFO - get_industry_analysis_params:609 - 行业分析PPT参数生成完成，共24个参数
2025-08-08 10:43:32 - api.PPTreport.constants - INFO - build_complete_ppt_data:412 - 行业分析数据已集成到PPT参数中，新增参数: 24
2025-08-08 10:43:32 - services.llm_service - INFO - __init__:78 - LLMService初始化完成，使用模型: qwen-max
2025-08-08 10:43:32 - api.PPTreport.PPTAi - INFO - __init__:22 - PPT AI分析器初始化完成
2025-08-08 10:43:32 - api.PPTreport.PPTAi - INFO - analyze_member_data_report:126 - 开始分析member_data_analysis_report...
2025-08-08 10:43:32 - services.llm_service - INFO - call:229 - 调用LLM，模型: qwen-max, enable_search=False
2025-08-08 10:43:32 - services.llm_service - INFO - call:230 - 消息: [{"role": "user", "content": "\n你是一位专业的会员运营数据分析师，请基于以下会员数据进行深度分析，从提供的模板句式中选择最合适的3个进行分析。\n\n## 数据统计时间范围\n**分析期间：数据统计期间**\n\n## 会员数据详情\n**基础数据：**\n- 会员总量：35,088人\n- 新增会员：9,159人（占比26.1%）\n- 历史会员：25929人\n...
2025-08-08 10:43:52 - services.llm_service - INFO - call:247 - LLM响应: ChatCompletion(id='chatcmpl-457bce18-6b5d-9872-903e-6f8fa891014e', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='1、会员基础资料完善度极低（0.2%），严重影响个性化服务提供。建议通过设置奖励机制鼓励会员补充个人信息。\n2、新增会员占比26.1%，但整体增长趋势需进一步明确。可考虑增加新用户注册激励，如首单优惠等吸引措施。\n3、尽管90.7%的会员有消费行为，但仅0.1%进行过充值。应设计更多吸引人的充值套餐或活动来提高这一比例。\n4、未完善手机号码的会员占3.1%，影响了与会员的有效沟通。可以通过短信提醒等方式促进信息更新。\n5、历史会员数量庞大，但具体活跃状况未知。建议定期发送定制化邮件或推送以激活沉默用户。', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754621032, model='qwen-max', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=156, prompt_tokens=920, total_tokens=1076, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=0)))
2025-08-08 10:43:52 - api.PPTreport.PPTAi - INFO - _validate_and_format_analysis:319 - AI分析结果格式化完成，共5条分析
2025-08-08 10:43:52 - api.PPTreport.PPTAi - INFO - analyze_member_data_report:137 - member_data_analysis_report分析完成: 1、会员基础资料完善度极低（0.2%），严重影响个性化服务提供。建议通过设置奖励机制鼓励会员补充个人信息。
2、新增会员占比26.1%，但整体增长趋势需进一步明确。可考虑增加新用户注册激励，如首单优惠等吸引措施。
3、尽管90.7%的会员有消费行为，但仅0.1%进行过充值。应设计更多吸引人的充值套餐或活动来提高这一比例。
4、未完善手机号码的会员占3.1%，影响了与会员的有效沟通。可以通过短信提醒等方式促进信息更新。
5、历史会员数量庞大，但具体活跃状况未知。建议定期发送定制化邮件或推送以激活沉默用户。
2025-08-08 10:43:52 - api.PPTreport.PPTAi - INFO - analyze_member_revenue_report:255 - 开始分析member_revenue_analysis_report...
2025-08-08 10:43:52 - services.llm_service - INFO - call:229 - 调用LLM，模型: qwen-max, enable_search=False
2025-08-08 10:43:52 - services.llm_service - INFO - call:230 - 消息: [{"role": "user", "content": "\n你是一位专业的财务数据分析师，请基于以下会员收入数据进行深度分析，从提供的模板句式中选择最合适的3个进行分析。\n\n## 数据统计时间范围\n**分析期间：数据统计期间**\n\n## 收入数据详情\n**收入规模：**\n- 总实际消费：1,080,938.58元\n- 储值消费：298,611.05元（占比27.6%）\n- 现...
2025-08-08 10:44:14 - services.llm_service - INFO - call:247 - LLM响应: ChatCompletion(id='chatcmpl-8aebc2d6-480f-98d6-b91f-8d1aba1681c4', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='1、收入结构中现金消费占比高达72.4%，表明储值机制吸引力不足；建议优化储值优惠政策，提高储值消费比例，增强客户粘性。\n2、重复消费金额占比54.4%，显示出良好的用户忠诚度但仍有提升空间；应进一步细分高价值用户群体，实施个性化营销策略，促进复购率增长。\n3、人均消费额为96元而平均单次消费仅51.28元，说明大部分用户消费频次较低；可通过增加会员专享活动或限时折扣来刺激消费频率，提升整体销售额。\n4、消费频次仅为1.88次，反映出顾客回访率偏低的问题；考虑引入积分奖励计划或设置定期促销活动，鼓励更多回头客，提高用户活跃度。\n5、首次消费金额与重复消费金额接近，显示新老客户贡献相当；需要加强对新客户的引导和教育工作，同时维护好现有客户关系，确保长期稳定的收入来源。', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754621055, model='qwen-max', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=206, prompt_tokens=921, total_tokens=1127, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=0)))
2025-08-08 10:44:14 - api.PPTreport.PPTAi - INFO - _validate_and_format_analysis:319 - AI分析结果格式化完成，共5条分析
2025-08-08 10:44:14 - api.PPTreport.PPTAi - INFO - analyze_member_revenue_report:266 - member_revenue_analysis_report分析完成: 1、收入结构中现金消费占比高达72.4%，表明储值机制吸引力不足；建议优化储值优惠政策，提高储值消费比例，增强客户粘性。
2、重复消费金额占比54.4%，显示出良好的用户忠诚度但仍有提升空间；应进一步细分高价值用户群体，实施个性化营销策略，促进复购率增长。
3、人均消费额为96元而平均单次消费仅51.28元，说明大部分用户消费频次较低；可通过增加会员专享活动或限时折扣来刺激消费频率，提升整体销售额。
4、消费频次仅为1.88次，反映出顾客回访率偏低的问题；考虑引入积分奖励计划或设置定期促销活动，鼓励更多回头客，提高用户活跃度。
5、首次消费金额与重复消费金额接近，显示新老客户贡献相当；需要加强对新客户的引导和教育工作，同时维护好现有客户关系，确保长期稳定的收入来源。
2025-08-08 10:44:14 - api.PPTreport.PPTReport - INFO - _convert_to_ppt_params:157 - 转换后的PPT参数数量: 152
2025-08-08 10:44:14 - api.PPTreport.PPTReport - INFO - generate_member_report:61 - 步骤3: 验证参数完整性...
2025-08-08 10:44:14 - api.PPTreport.PPTReport - INFO - generate_member_report:83 - 步骤4: 生成PPT文件...
2025-08-08 10:44:14 - services.ppt_service - INFO - generate_ppt_report:98 - 开始生成PPT报告...
2025-08-08 10:44:14 - services.ppt_service - INFO - generate_ppt_report:121 - 验证PPT模板参数...
2025-08-08 10:44:14 - services.ppt_service - INFO - generate_ppt_report:128 - 额外的参数: ['image_chart', 'image_coupon', 'image_logo', 'image_revenue', 'image_test', 'image_trend', 'report_date']
2025-08-08 10:44:14 - services.ppt_service - INFO - generate_ppt_report:131 - 开始生成PPT文件...
2025-08-08 10:44:14 - simple_ppt_generator - INFO - generate_report:74 - 复制模板文件: C:\Users\<USER>\Desktop\member_report_test\member-api\ppt_template\会员数据报告-模板.pptx -> C:\Users\<USER>\Desktop\member_report_test\member-api\uploads\temp\会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:14 - simple_ppt_generator - INFO - generate_report:78 - 开始替换参数...
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片1: {time_frame} -> 2025年1月1日-6月30日
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片1: {time_frame} -> 2025年1月1日-6月30日
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {member_data_analysis_report} -> 1、会员基础资料完善度极低（0.2%），严重影响个性化服务提供。建议通过设置奖励机制鼓励会员补充个人信息。
2、新增会员占比26.1%，但整体增长趋势需进一步明确。可考虑增加新用户注册激励，如首单优惠等吸引措施。
3、尽管90.7%的会员有消费行为，但仅0.1%进行过充值。应设计更多吸引人的充值套餐或活动来提高这一比例。
4、未完善手机号码的会员占3.1%，影响了与会员的有效沟通。可以通过短信提醒等方式促进信息更新。
5、历史会员数量庞大，但具体活跃状况未知。建议定期发送定制化邮件或推送以激活沉默用户。
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {new_members} -> 9159
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {total_members} -> 35088
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {historical_members} -> 25929
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {complete_phone_members} -> 33996
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {total_complete_members} -> 59
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {total_consume_members} -> 31814
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {total_charge_members} -> 46
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {complete_nophone_members} -> 1092
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {total_nocomplete_members} -> 35029
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {total_noconsume_members} -> 3274
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {total_nocharge_members} -> 35042
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {unfollow_members} -> 34673
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片6: {net_members} -> 415
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {member_revenue_analysis_report} -> 1、收入结构中现金消费占比高达72.4%，表明储值机制吸引力不足；建议优化储值优惠政策，提高储值消费比例，增强客户粘性。
2、重复消费金额占比54.4%，显示出良好的用户忠诚度但仍有提升空间；应进一步细分高价值用户群体，实施个性化营销策略，促进复购率增长。
3、人均消费额为96元而平均单次消费仅51.28元，说明大部分用户消费频次较低；可通过增加会员专享活动或限时折扣来刺激消费频率，提升整体销售额。
4、消费频次仅为1.88次，反映出顾客回访率偏低的问题；考虑引入积分奖励计划或设置定期促销活动，鼓励更多回头客，提高用户活跃度。
5、首次消费金额与重复消费金额接近，显示新老客户贡献相当；需要加强对新客户的引导和教育工作，同时维护好现有客户关系，确保长期稳定的收入来源。
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {total_actual_amount} -> 1,080,938.58
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {prepay_actual_amount} -> 298,611.05
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {actual_amount} -> 782,327.53
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {consume_frequency} -> 1.88
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {avg_contribution} -> 96.43
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {consume_users} -> 11210
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {consume_frequency} -> 1.88
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {avg_consume_amount} -> 51.28
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {first_consume_amount} -> 492,889.30
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {repeat_consume_amount} -> 588,049.28
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {total_actual_revenue} -> 0.00
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {total_expected_revenue} -> 0.00
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {discount_rate} -> 0.00%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {dine_in_actual_revenue} -> 0.00
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {takeout_actual_revenue} -> 0.00
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {non_member_total_actual_amount} -> 0.00
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片8: {member_dine_in_ratio} -> 0.00%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片9: {new_member_add_last_year_analysis_report} -> 1-2024年全年新增会员达23831人月均增长1986人表明整体增长态势良好建议通过调查问卷等方式收集用户偏好进一步优化营销内容提高吸引力
2-观察到2024年4月(3851人)与2月(969人)之间存在显著差异显示出明显的季节性波动特征建议针对不同月份特点设计促销活动如在淡季增加互动性强的线上活动来维持用户活跃度
3-取关率保持在非常低的水平仅为0.08%显示了良好的用户留存状况建议利用数据分析工具深入探究用户行为模式识别高价值用户群体并为其提供个性化服务以增强黏性
4-虽然企业微信加好友数为847人占总新增量比例较小但这是一个重要的私域流量入口建议加强企微账号的品牌建设和功能开发同时开展更多线上线下结合的活动促进用户从公域向私域转化
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片9: 图片参数 {image_new_member_add_last_year} -> uploads\temp\**********_20250808\new_member_add_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\new_member_add_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片10: 图片参数 {image_new_member_add_this_year} -> uploads\temp\**********_20250808\new_member_add_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片10: {new_member_add_this_year_analysis_report} -> 1. 2025年新增会员数较2024年下降31.48%，建议开展用户调研了解需求变化，针对性调整产品或服务，并通过社交媒体增加曝光度以吸引新用户
2. 2025年的平均取关率从0.07%降至0.01%，表明会员满意度可能有所提升，但仍需加强个性化推荐系统建设，进一步提高用户体验与忠诚度
3. 与去年相比，企微加好友数量由847降至0人，显示该渠道获客能力显著减弱，应重新评估并优化企业微信运营策略，比如增加互动活动频率、优化内容质量等措施来激活此渠道
4. 2025年7月新增会员达到峰值3,123人后，8月骤降至457人，显示出明显的季节性波动特征，建议提前规划旺季营销活动同时探索淡季增长点，如推出限时优惠或举办线上线下结合的社区活动来稳定全年增长
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\new_member_add_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片11: {member_consumption_last_year_analysis_report} -> 1-2024年4月总实收33.43万元远高于其他月份，表明存在显著季节性需求增长建议针对此期间加大促销力度同时提前备货以满足高峰期需求
2-从2024年1月至12月储值消费金额逐步增加由0.70万元至4.45万元显示会员忠诚度提高策略上应进一步优化储值方案如提供更多优惠或积分回馈以吸引更多用户参与储值
3-对比2024年8月(24.45万元)与9月(17.60万元)发现短时间内总实收下降明显需调查背后原因可能是产品吸引力不足或是竞争对手活动影响建议开展客户满意度调研并根据反馈调整产品线或增强营销攻势
4-尽管全年总实收达到239.05万元但月均现金消费占比高达78.8%，说明仍有较大空间通过推广电子支付等方式降低运营成本同时可以考虑推出更多针对非现金支付用户的专属优惠来平衡支付方式结构
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片11: 图片参数 {image_member_consumption_last_year} -> uploads\temp\**********_20250808\member_consumption_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\member_consumption_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片12: {member_consumption_this_year_analysis_report} -> 1-消费增长承压：2025年月均总实收17.50万元，比2024年的21.63万元下降了19.08%，建议紧急制定促销活动和会员激励计划以提升销售额
1-储值消费增长突出：从2024年的月均3.81万元增加到2025年的4.49万元，增幅达17.62%，建议进一步优化储值服务流程，并引入更多高吸引力的储值奖励项目
1-现金支付占比下降：2025年现金消费占总消费比例为74.4%，相比去年的82.3%有所减少，反映出消费者偏好变化，推荐加强移动支付渠道建设同时保持对现金用户的关注
1-季节性波动加剧：特别是比较2025年8月与前几个月及去年同期数据，显示显著下滑趋势，建议深入调查夏季淡季影响因素并提前规划应对策略
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片12: 图片参数 {image_member_consumption_this_year} -> uploads\temp\**********_20250808\member_consumption_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\member_consumption_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片13: {avg_consumption_last_year_analysis_report} -> 1. 2024年1月至2月现金消费笔数显著下降从1251降至1010，同时单均消费额保持稳定约890元，表明春节假期可能影响了消费者行为但未改变其消费意愿建议针对节假日推出限时促销活动以吸引顾客增加访问频率
2. 2024年3月储值消费笔数达到191笔为年内最高，且当月单均消费降至596.10元显示通过储值方式可以有效促进小额高频次购买鼓励更多会员使用储值卡并通过积分奖励等措施提高储值吸引力
3. 从2024年4月至12月观察到单均消费持续下降由403.70元跌至164.24元，而人均贡献则在同期内波动上升尤其是11月达到峰值88.01元说明尽管单笔交易金额减少但整体客户基数或复购率有所增长企业应进一步分析用户留存策略并加强个性化营销以维持甚至提升当前的消费频次
4. 2024年10月至12月间虽然单均消费略有回升但仍处于较低水平分别为164.22、160.42及164.24元，同时人均贡献也表现出先增后减的趋势这可能是由于年底促销活动导致单价降低但并未充分转化为更高的人均销售额建议调整促销策略更加注重于提高客单价而非单纯追求销量
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片13: 图片参数 {image_avg_consumption_last_year} -> uploads\temp\**********_20250808\avg_consumption_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\avg_consumption_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片14: {avg_consumption_this_year_analysis_report} -> 1. 2025年现金消费笔数较去年同期下降了23.49%，月均减少至2336笔，建议通过增加支付渠道多样性如引入更多第三方支付平台，并开展针对现金支付用户的优惠活动来提升其使用频率。
2. 储值消费笔数从2024年的月均731笔增长至2025年的1009笔，增幅达38%，表明储值策略有效，但需进一步细化不同层级会员的储值奖励机制，特别是针对高价值客户推出专属储值方案以促进更大规模的资金沉淀。
3. 2025年的单均消费额为179.24元，比2024年的467.72元降低了约62%，显示出消费者对价格敏感度提高，建议企业调整产品结构，引入更多性价比高的商品同时设计分级定价体系满足不同消费能力顾客需求；此外还可以定期举办满减或折扣促销活动刺激购买欲望。
4. 尽管2025年人均贡献略有上升至76.13元，但与2024年相比仅增长了4.5%。为了进一步挖掘每位顾客的价值潜力，可以考虑实施更加个性化的营销沟通策略，比如基于用户历史购买行为推送定制化推荐信息；同时加强线上线下融合体验建设，增强品牌忠诚度。
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片14: 图片参数 {image_avg_consumption_this_year} -> uploads\temp\**********_20250808\avg_consumption_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\avg_consumption_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片15: {consumption_num_last_year_analysis_report} -> 1. 2024年4月消费人数达到峰值3822人，随后逐渐下降至12月的2395人，表明存在显著季节性波动，建议针对下半年尤其是Q4推出定制化促销活动以刺激消费
2. 从2024年1月至12月，平均消费频次为1.37次/人，其中2024年4月最高达1.64次/人，揭示了特定月份内用户活跃度增加的可能性，建议通过数据分析识别该月吸引用户的因素，并在其他月份复制成功经验
3. 2024年内充值笔数占总消费笔数比例约为9.9%，特别是2024年4月和5月充值笔数分别为403笔和501笔相对较高，提示这些时期可能存在更多充值需求或优惠活动有效促进了充值行为，建议增强非高峰期的充值激励措施来平衡全年充值率
4. 虽然整体上消费人数与消费笔数保持稳定增长趋势，但如2024年9月至12月间消费人数由2360人降至最低点2091人后再回升至2395人，显示了潜在的顾客流失风险，建议加强客户关系管理，实施忠诚度计划以提高用户粘性
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片15: 图片参数 {image_consumption_num_last_year} -> uploads\temp\**********_20250808\consumption_num_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\consumption_num_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片16: {consumption_num_this_year_analysis_report} -> 1-2025年消费人数月均2513人比2024年的2786人减少9.82%，建议通过市场调研了解流失原因，针对发现的问题优化产品或服务体验。
2-2025年4月至6月消费频次从1.44次/人升至1.45次/人再降至1.44次/人，虽略有波动但总体稳定，建议推出季节性促销活动吸引顾客多次消费。
3-2025年充值笔数月均为343笔相较于2024年的335笔增长了2.24%，表明当前充值激励措施有效，建议进一步细化会员等级制度，为高级会员提供更多专属优惠以促进充值。
4-对比2025年7月与2024年同期，消费人数显著增加（4030 vs 3976），但进入8月后急剧下降至797人，建议深入调查7月至8月间可能影响客户行为变化的因素，如外部竞争加剧或内部运营问题，并据此调整下半年营销策略。
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片16: 图片参数 {image_consumption_num_this_year} -> uploads\temp\**********_20250808\consumption_num_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\consumption_num_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片17: {member_charge_last_year_analysis_report} -> 1. 2024年7月充值金额达到峰值81900.00万元远超其他月份，建议详细分析该月营销活动与用户互动策略，并在接下来的几个月如2024年1月至3月期间复用这些成功经验以提升整体充值水平
2. 储值消耗率高达84.45%，显示用户对储值服务的高度认可，但为了进一步提高用户粘性，建议从2024年第二季度开始推出更多个性化或限时优惠储值套餐吸引更多消费
3. 当前无任何充值赠送活动导致赠送比例为0.00%，这可能限制了部分用户的充值积极性，提议自2024年4月起实施小额随机红包返还机制，比如每满1000元随机返现10-50元，以此激励更多小额及中等额度的充值行为
4. 2024年4月留存率达到最高点32.77%，而到了同年12月则降至最低5.72%，表明年内存在显著波动，应深入研究4月份采取的具体措施（例如是否有特别促销、客户服务改进等），并考虑在2024年下半年特别是10至12月间复制这些有效做法来稳定甚至提升留存率
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片17: 图片参数 {image_member_charge_last_year} -> uploads\temp\**********_20250808\member_charge_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\member_charge_last_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片18: {member_charge_this_year_analysis_report} -> 1. 2025年充值规模同比增加8.82%，但增长放缓，建议引入灵活多变的充值套餐和促销活动以刺激消费
2. 2025年的储值消耗率比去年同期提高6.49个百分点至86.74%，表明用户活跃度提升，建议进一步丰富储值应用场景，比如增加会员专享服务或商品
3. 连续两年充值赠送均为零，考虑到赠送策略对吸引新用户及激励老用户的有效性，建议启动基于消费行为分析的小额随机红包或积分奖励计划
4. 与去年相比，2025年的平均储值留存率下降了9.37个百分点，特别是从2024年4月的32.77%降至2025年4月的19.06%，反映出客户忠诚度可能有所下滑，应加强个性化营销和售后服务，比如提供专属客服支持、建立VIP俱乐部等措施来增强用户粘性
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片18: 图片参数 {image_member_charge_this_year} -> uploads\temp\**********_20250808\member_charge_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\member_charge_this_year.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片19: 图片参数 {image_level_number} -> uploads\temp\**********_20250808\level_number.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片19: {level_number_analysis_report} -> 从2024年12月31日至2025年6月30日，会员总数由25929人增长至35088人，增幅达35.32%，其中普通会员增加8357人成为最大增长点，表明基础用户群体的显著扩张。储值会员和积分会员分别以45.1%和50.0%的增长率紧随其后，显示出较高忠诚度用户的积极反馈。然而，中海通专享卡及两种畅吃卡会员数量保持不变，暗示这些高端或特定需求服务可能面临市场饱和或吸引力不足的问题。建议针对未见增长的等级进行服务内容优化或市场调研，探索潜在客户需求；同时，利用数据分析进一步挖掘普通会员向更高级别转化的可能性，比如通过个性化推荐、定制化优惠等方式促进升级，从而实现会员结构的整体优化与价值提升。
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\level_number.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片20: 图片参数 {image_level_consumption} -> uploads\temp\**********_20250808\level_consumption.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片20: {level_consumption_analysis_report} -> 在2025-01-01至2025-06-30期间，储值会员以人均消费额178.87元领先，表明其作为高价值群体的重要性。普通会员与之相比存在显著差距，人均消费仅80.97元，揭示了会员体系中潜在的价值分层机会。从整体看，等级间的人均消费额、客单价及消费频次差异明显，这不仅验证了当前会员分层的有效性，也提示了优化空间。建议针对储值会员推出个性化增值服务或专属活动，增强其品牌归属感；同时，对于低活跃度的普通会员，可以通过设置积分奖励机制或阶段性促销活动激发其消费潜力。此外，应定期回顾各等级会员的行为模式，适时调整权益配置，确保持续吸引并保留高质量用户。
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\level_consumption.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:116 - 幻灯片21: 图片参数 {image_level_order} -> uploads\temp\**********_20250808\level_order.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片21: {level_order_analysis_report} -> 在2025年上半年，普通会员展现出显著的消费能力和订单贡献，分别占总消费金额和订单量的63.73%与65.60%，但其复购率仅为22.89%，远低于其他等级，表明虽然吸引了大量一次性消费者，但忠诚度提升空间大。相比之下，畅吃卡-安贞年卡尽管消费金额占比仅1.66%，订单占比也较低为1.90%，却拥有最高的复购率66.43%，显示出极高的用户粘性。储值会员表现均衡，消费与订单贡献均超过三分之一，且复购率达到56.80%，是维持业绩稳定的关键力量。针对上述情况，建议通过优化普通会员体验增加复购激励措施，同时加大畅吃卡-安贞年卡推广力度，吸引更多长期价值客户；对于储值会员，则应保持现有优势，并探索更多增值服务以进一步提高其活跃度。
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:149 - 成功插入图片: uploads\temp\**********_20250808\level_order.png
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {time_frame} -> 2025年1月1日-6月30日
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {total_send_count} -> 16630张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {total_used_count} -> 1854张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {avg_usage_rate} -> 11.1%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {total_drive_amount} -> 117,281.00元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id1} -> 100344774
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id2} -> 100344773
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id3} -> 100343206
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id5} -> 100358367
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id4} -> 100346480
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id6} -> 100358368
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id7} -> 100358366
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id8} -> 100370073
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name1} -> 10元代金券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name2} -> 5元代金券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name3} -> 香煎三文鱼半价券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name4} -> 牛气肥腹烧肉饭半价券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name5} -> 5元午市代金券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name6} -> 10元晚市代金券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name7} -> 炸虾天妇罗兑换券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name8} -> 消费30元返3元代金券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count1} -> 7257张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count2} -> 3548张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count3} -> 3371张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count4} -> 749张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count5} -> 576张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count6} -> 576张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count7} -> 288张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count8} -> 110张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count2} -> 94张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count3} -> 268张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count4} -> 499张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count5} -> 81张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count6} -> 108张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count7} -> 167张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count8} -> 3张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count1} -> 62张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate2} -> 2.6%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate3} -> 8.0%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate4} -> 66.6%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate5} -> 14.1%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate6} -> 18.8%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate7} -> 58.0%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate8} -> 2.7%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate1} -> 0.9%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount2} -> 6,030.80元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount3} -> 22,928.00元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount4} -> 33,722.50元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount5} -> 4,191.60元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount6} -> 6,934.10元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount7} -> 8,943.60元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount8} -> 129.00元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount1} -> 4,645.40元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id9} -> 100370040
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_id10} -> 100273449
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name9} -> 消费50元返5元代金券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_name10} -> 5元代金券
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count9} -> 91张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_send_count10} -> 38张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count9} -> 2张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_used_count10} -> 35张
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate9} -> 2.2%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {coupon_usage_rate10} -> 92.1%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount9} -> 103.00元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片24: {drive_total_amount10} -> 1,873.00元
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {prepay_member_ratio_year} -> 0.12%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_once_members_ratio_year} -> 71.20%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {prepay_member_ratio} -> 0.13%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_once_members_ratio} -> 68.18%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {phone_member_ratio_year} -> 97.23%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {phone_member_ratio} -> 96.89%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_member_ratio_year} -> 0.88%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_member_ratio} -> 0.96%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_twice_members_ratio_year} -> 14.08%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_twice_members_ratio} -> 15.60%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_thrice_members_ratio_year} -> 5.44%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_thrice_members_ratio} -> 6.09%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_more_than_thrice_members_ratio_year} -> 9.27%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_more_than_thrice_members_ratio} -> 10.12%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {repurchase_rate_year} -> 28.80%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {repurchase_rate} -> 31.82%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_frequency_year} -> 1.82
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {consume_frequency} -> 1.88
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {prepay_consumption_ratio_year} -> 25.63%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {prepay_consumption_ratio} -> 27.63%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {coupon_usage_rate_year} -> 11.34%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {coupon_usage_rate} -> 11.15%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {prepay_retention_rate_year} -> 13.26%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:127 - 幻灯片25: {prepay_retention_rate} -> 13.35%
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:154 - 保存文件: C:\Users\<USER>\Desktop\member_report_test\member-api\uploads\temp\会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - simple_ppt_generator - INFO - generate_report:157 - 报告生成成功! 共替换了 136 个文本参数，13 个图片参数
2025-08-08 10:44:15 - services.ppt_service - INFO - generate_ppt_report:136 - PPT报告生成成功 - 文件: C:\Users\<USER>\Desktop\member_report_test\member-api\uploads\temp\会员数据报告_品牌**********_20250808_103637.pptx, 大小: 10805.6 KB
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:109 - 文件上传成功: C:\Users\<USER>\Desktop\member_report_test\member-api\uploads\temp\会员数据报告_品牌**********_20250808_103637.pptx -> uploads\ppt-reports\会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:110 - OSS上传详情:
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:111 -   - 源文件: C:\Users\<USER>\Desktop\member_report_test\member-api\uploads\temp\会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:112 -   - 目标路径: uploads\ppt-reports\会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:113 -   - 对象名: 会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:114 -   - 文件夹: ppt-reports
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:115 -   - 生成的URL: http://localhost:8000/api/ppt-report/download/ppt-reports/会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - services.oss_service - INFO - upload_file:116 -   - base_url: http://localhost:8000
2025-08-08 10:44:15 - services.ppt_service - INFO - generate_ppt_report:142 - PPT文件上传成功: http://localhost:8000/api/ppt-report/download/ppt-reports/会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - services.ppt_service - INFO - generate_ppt_report:148 - 临时文件已删除: C:\Users\<USER>\Desktop\member_report_test\member-api\uploads\temp\会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - api.PPTreport.PPTReport - INFO - generate_member_report:87 - PPT报告生成成功: uploads\ppt-reports\会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - api.PPTreport.router - INFO - generate_member_report:86 - PPT生成结果详情:
2025-08-08 10:44:15 - api.PPTreport.router - INFO - generate_member_report:87 -   - 文件名: 会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - api.PPTreport.router - INFO - generate_member_report:88 -   - 对象名: None
2025-08-08 10:44:15 - api.PPTreport.router - INFO - generate_member_report:89 -   - 原始下载URL: None
2025-08-08 10:44:15 - api.PPTreport.router - INFO - generate_member_report:90 -   - 所有返回数据: {'file_path': 'uploads\\ppt-reports\\会员数据报告_品牌**********_20250808_103637.pptx', 'file_name': '会员数据报告_品牌**********_20250808_103637.pptx', 'file_size': 11064911, 'query_params': {'query_type': <QueryTypeEnum.CUSTOM: 'custom'>, 'bid': '**********', 'sid': None, 'start_date': '2025-01-01', 'end_date': '2025-06-30', 'cashier_system': '0', 'merchant_id': None, 'time_frame': '2025年1月1日-6月30日'}, 'validation_result': {'valid': True, 'missing_params': [], 'total_params': 152, 'required_params': ['time_frame', 'report_date', 'member_data_analysis_report', 'total_members', 'new_members', 'member_revenue_analysis_report']}, 'missing_params': [], 'extra_params': ['image_revenue', 'image_chart', 'image_trend', 'image_coupon', 'report_date', 'image_test', 'image_logo']}
2025-08-08 10:44:15 - api.PPTreport.router - WARNING - generate_member_report:104 - PPT生成成功但下载URL为空，尝试生成默认URL
2025-08-08 10:44:15 - api.PPTreport.router - INFO - generate_member_report:110 - 使用文件名生成默认下载URL: /api/ppt-report/download/ppt-reports/会员数据报告_品牌**********_20250808_103637.pptx
2025-08-08 10:44:15 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/ppt-report/generate - 状态码: 200 - 耗时: 458.112秒
2025-08-08 12:38:38 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-08 12:38:38 - request - INFO - log_requests:71 - 请求头: {'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.8786.87 Safari/537.36', 'accept-encoding': 'gzip, x-gzip, deflate', 'host': '*************:8000', 'connection': 'keep-alive'}
2025-08-08 12:38:38 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.001秒
2025-08-08 12:40:36 - request - INFO - log_requests:70 - 收到请求: GET http://ossweb-img.qq.com/images/js/eas/eas.js
2025-08-08 12:40:36 - request - INFO - log_requests:71 - 请求头: {'host': 'ossweb-img.qq.com', 'user-agent': 'Mozilla', 'if-modified-since': 'Wed, 14 Jun 2023 09:24:00 GMT'}
2025-08-08 12:40:36 - request - INFO - log_requests:78 - 请求处理完成: GET http://ossweb-img.qq.com/images/js/eas/eas.js - 状态码: 404 - 耗时: 0.000秒
2025-08-08 12:59:24 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-08 12:59:24 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Hello from Palo Alto Networks, find out more about our scans in https://docs-cortex.paloaltonetworks.com/r/1/Cortex-Xpanse/Scanning-activity', 'accept-encoding': 'gzip'}
2025-08-08 12:59:24 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.001秒
2025-08-08 13:14:19 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-08 13:14:19 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)', 'accept': '*/*', 'accept-encoding': 'gzip'}
2025-08-08 13:14:19 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.001秒
2025-08-08 13:14:21 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/favicon.ico
2025-08-08 13:14:21 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)', 'accept-encoding': 'gzip', 'connection': 'close'}
2025-08-08 13:14:21 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/favicon.ico - 状态码: 404 - 耗时: 0.001秒
2025-08-08 13:14:27 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-08 13:14:27 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)', 'accept': '*/*', 'accept-encoding': 'gzip'}
2025-08-08 13:14:27 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.001秒
2025-08-08 14:36:02 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-08 14:36:02 - request - INFO - log_requests:71 - 请求头: {'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.7062.88 Safari/537.36', 'accept-encoding': 'gzip, x-gzip, deflate', 'host': '*************:8000', 'connection': 'keep-alive'}
2025-08-08 14:36:02 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.001秒
2025-08-08 16:50:35 - core.database - INFO - disconnect:205 - dwoutput数据库连接池已关闭
2025-08-08 16:50:35 - core.database - INFO - disconnect:210 - wedatas数据库连接池已关闭
2025-08-08 16:50:35 - core.database - INFO - disconnect:215 - welife_hydb数据库连接池已关闭
2025-08-08 16:50:35 - core.database - INFO - disconnect:220 - basic_info数据库连接池已关闭
2025-08-08 16:50:35 - core.database - INFO - disconnect:225 - backend数据库连接池已关闭
2025-08-08 16:50:35 - core.database - INFO - disconnect:229 - 品质收银数据库连接池已关闭
2025-08-08 16:50:35 - main - INFO - lifespan:33 - 应用已关闭
